<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.ItemInfoMapper">

    <resultMap type="org.dromara.servicetrack.domain.vo.ItemListVo" id="ItemInfoListResult">
        <id property="id" column="key_id"/>
        <result property="itemId" column="item_id"/>
        <result property="displayId" column="display_id"/>
        <result property="itemTitle" column="item_title"/>
        <result property="moduleId" column="module_id"/>
        <result property="ownerId" column="owner_id"/>
        <result property="stateId" column="state_id"/>
        <result property="typeId" column="type_id"/>
        <result property="createdTime" column="created_time"/>
        <result property="createdBy" column="created_by"/>
        <result property="modifiedTime" column="modified_time"/>
        <result property="modifiedBy" column="modified_by"/>
        <result column="contact_id" property="contactId"/>

        <result property="textFieldsJson" column="text_fields"/>
        <result property="datetimeFieldsJson" column="datetime_fields"/>
        <result property="selectionFieldsJson" column="selection_fields"/>

    </resultMap>
    <resultMap id="ItemDetailMap" type="org.dromara.servicetrack.domain.vo.ItemInfoVo">
        <!-- 基础字段映射 -->
        <id property="id" column="key_id"/>
        <id column="project_id" property="projectId"/>
        <id column="item_id" property="itemId"/>
        <result column="module_id" property="moduleId"/>
        <result column="display_id" property="displayId"/>
        <result column="item_title" property="itemTitle"/>
        <result column="owner_id" property="ownerId"/>
        <result column="state_id" property="stateId"/>
        <result column="type_id" property="typeId"/>
        <result column="created_time" property="createdTime"/>
        <result column="created_by" property="createdBy"/>
        <result column="modified_time" property="modifiedTime"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="assigned_time" property="assignedTime"/>
        <result column="contact_id" property="contactId"/>
        <!-- 自定义字段直接映射为字符串 -->
        <result column="datetime_fields" property="datetimeFieldsJson"/>
        <result column="text_fields" property="textFieldsJson"/>
        <result column="selection_fields" property="selectionFieldsJson"/>
    </resultMap>
    <select id="selectPageItemList" resultMap="ItemInfoListResult">

        <if test="(textFieldIds != null and textFieldIds.size() > 0) || (dateTimeFieldIds != null and dateTimeFieldIds.size() > 0) || (selectionFieldIds != null and selectionFieldIds.size() > 0)">
        WITH
        <trim suffixOverrides=",">
            <if test="textFieldIds != null and textFieldIds.size() > 0">
                text_fields AS (
                SELECT project_id, item_id,
                GROUP_CONCAT(
                CONCAT('"', field_id, '":"', REPLACE(text, '"', '\\"'), '"')
                SEPARATOR ','
                ) AS text_values,
                MIN(text) AS min_text -- 添加一列用于排序
                FROM item_text
                WHERE project_id = #{projectId} AND
                item_id IN (SELECT item_id FROM item_info u WHERE project_id = #{projectId}
                <if test="itemSqlSegment != null and itemSqlSegment != ''">
                    and ${itemSqlSegment}
                </if> )
                AND
                field_id IN
                <foreach item="fieldId" collection="textFieldIds" open="(" separator="," close=")">
                    #{fieldId}
                </foreach>
                GROUP BY project_id, item_id
                ),
            </if>
            <if test="dateTimeFieldIds != null and dateTimeFieldIds.size() > 0">

                datetime_fields AS (
                SELECT project_id, item_id,
                GROUP_CONCAT(
                CONCAT('"', field_id, '":"', datetime, '"')
                SEPARATOR ','
                ) AS datetime_values,
                MIN(datetime) AS min_datetime -- 添加一列用于排序
                FROM item_datetime
                WHERE  project_id = #{projectId} AND
                item_id IN (SELECT item_id FROM item_info u WHERE project_id = #{projectId}
                <if test="itemSqlSegment != null and itemSqlSegment != ''">
                    and ${itemSqlSegment}
                </if> )
                AND  field_id IN
                <foreach item="fieldId" collection="dateTimeFieldIds" open="(" separator="," close=")">
                    #{fieldId}
                </foreach>
                GROUP BY project_id, item_id
                ),
            </if>
            <if test="selectionFieldIds != null and selectionFieldIds.size() > 0">
                selection_fields AS (
                SELECT project_id, item_id, field_id,
                CONCAT('[', GROUP_CONCAT(choice_id ORDER BY choice_id), ']') AS choices,
                MIN(choice_id) as min_choice_id
                FROM item_selection
                WHERE  project_id = #{projectId} AND
                item_id IN (SELECT item_id FROM item_info u WHERE project_id = #{projectId}
                <if test="itemSqlSegment != null and itemSqlSegment != ''">
                    and ${itemSqlSegment}
                </if> )
                AND  field_id IN
                <foreach item="fieldId" collection="selectionFieldIds" open="(" separator="," close=")">
                    #{fieldId}
                </foreach>
                GROUP BY project_id, item_id, field_id
                ),
                aggregated_selection AS (
                SELECT
                project_id,
                item_id,
                CONCAT('{', GROUP_CONCAT(CONCAT('"', field_id, '":', choices) SEPARATOR ','), '}') AS selection_data
                FROM
                selection_fields
                GROUP BY
                project_id, item_id
                ),
                filtered_fs AS (
                SELECT
                u.key_id,
                u.project_id,
                u.item_id,
                MIN(fs.choice_name) AS choice_name -- 确保每个 item 唯一
                FROM
                item_info u
                LEFT JOIN
                selection_fields s
                ON
                u.project_id = s.project_id AND u.item_id = s.item_id
                LEFT JOIN
                project_field_selection fs
                ON
                s.project_id = fs.project_id AND s.field_id = fs.field_id AND s.min_choice_id = fs.choice_id
                WHERE
                 u.project_id = #{projectId}
                <if test="itemSqlSegment != null and itemSqlSegment != ''">
                    and ${itemSqlSegment}
                </if>
                GROUP BY
                u.key_id, u.project_id, u.item_id
                )
            </if>
        </trim>
        </if>
        select
        <if test="ew.getSqlSelect != null">
            ${ew.getSqlSelect}
        </if>
        <if test="ew.getSqlSelect == null">
            DISTINCT u.key_id, u.project_id, u.item_id, u.display_id, u.item_title, u.module_id, u.owner_id, u.state_id,
            u.type_id, u.created_time, u.created_by, u.modified_time, u.modified_by, u.assigned_time, u.contact_id
            <if test="sortFieldId != null and sortFieldId == 3">
                ,st.state_name
            </if>
            <if test="sortFieldId != null and sortFieldId == 4">
                ,ownerUser.nick_name
            </if>
            <if test="sortFieldId != null and sortFieldId == 20">
                ,submittedUser.nick_name
            </if>
            <if test="sortFieldId != null and sortFieldId == 21">
                ,modifiedUser.nick_name
            </if>
            <if test="sortFieldId != null and sortFieldId == 6">
                ,employeeUser.nick_name
            </if>
            <if test="textFieldIds != null and textFieldIds.size() > 0">
               , COALESCE(t.text_values, '{}') AS text_fields, min_text
            </if>
            <if test="dateTimeFieldIds != null and dateTimeFieldIds.size() > 0">
                ,COALESCE(d.datetime_values, '{}') AS datetime_fields, min_datetime
            </if>
            <if test="selectionFieldIds != null and selectionFieldIds.size() > 0">
                ,(
                SELECT CONCAT('{', GROUP_CONCAT(
                CONCAT('"', field_id, '":', choices)
                SEPARATOR ','
                ), '}')
                FROM selection_fields s
                WHERE s.project_id = u.project_id AND s.item_id = u.item_id
                ) AS selection_fields,
                fs.choice_name -- 添加到 SELECT 列表中
            </if>
        </if>
        from item_info u
        <if test="textFieldIds != null and textFieldIds.size() > 0">
            LEFT JOIN text_fields t ON u.project_id = t.project_id AND u.item_id = t.item_id
        </if>
        <if test="dateTimeFieldIds != null and dateTimeFieldIds.size() > 0">
            LEFT JOIN datetime_fields d ON u.project_id = d.project_id AND u.item_id = d.item_id
        </if>
        <if test="selectionFieldIds != null and selectionFieldIds.size() > 0">
            LEFT JOIN aggregated_selection s  ON u.project_id = s.project_id AND u.item_id = s.item_id
            LEFT JOIN filtered_fs fs ON u.project_id = fs.project_id AND u.item_id = fs.item_id
        </if>
        <choose>
            <when test="sortFieldId != null and sortFieldId == 3">
                LEFT JOIN workflow_state st
                ON u.project_id = st.project_id
                AND u.state_id = st.state_id
            </when>
            <when test="sortFieldId != null and sortFieldId == 4">
                LEFT JOIN sys_user ownerUser
                ON u.owner_id = ownerUser.external_user_id
            </when>
            <when test="sortFieldId != null and sortFieldId == 20">
                LEFT JOIN sys_user submittedUser
                ON u.owner_id = submittedUser.external_user_id
            </when>
            <when test="sortFieldId != null and sortFieldId == 21">
                LEFT JOIN sys_user modifiedUser
                ON u.modified_by = modifiedUser.external_user_id
            </when>
            <when test="sortFieldId != null and sortFieldId == 6">
                LEFT JOIN sys_user employeeUser
                ON u.contact_id = employeeUser.external_user_id
            </when>
<!--            <when test="sortFieldId != null and sortFieldId == -10001">-->
<!--                LEFT JOIN item_text t ON u.project_id = t.project_id AND u.item_id = t.item_id-->
<!--            </when>-->
<!--            <when test="sortFieldId != null and sortFieldId == -10002">-->
<!--                LEFT JOIN item_datetime d ON u.project_id = d.project_id AND u.item_id = d.item_id-->
<!--            </when>-->

        </choose>
        ${ew.getCustomSqlSegment}
    </select>
    <select id="getItemDetail" resultMap="ItemDetailMap">
        WITH
        base_item AS (
        SELECT *
        FROM item_info
        WHERE project_id = #{projectId} AND item_id = #{itemId}
        ),
        datetime_fields AS (
        SELECT
        project_id,
        item_id,
        GROUP_CONCAT(
        CONCAT('"', field_id, '":"', datetime, '"')
        SEPARATOR ','
        ) as datetime_values
        FROM item_datetime
        WHERE project_id = #{projectId} AND item_id = #{itemId}
        GROUP BY project_id, item_id
        ),
        text_fields AS (
        SELECT
        project_id,
        item_id,
        GROUP_CONCAT(
        CONCAT('"', field_id, '":"', REPLACE(text, '"', '\\"'), '"')
        SEPARATOR ','
        ) as text_values
        FROM item_text
        WHERE project_id = #{projectId} AND item_id = #{itemId}
        GROUP BY project_id, item_id
        ),
        selection_fields AS (
        SELECT
        project_id,
        item_id,
        field_id,
        CONCAT('[', GROUP_CONCAT(choice_id ORDER BY choice_id), ']') as choices
        FROM item_selection
        WHERE project_id = #{projectId} AND item_id = #{itemId}
        GROUP BY project_id, item_id, field_id
        )
        SELECT
        i.*,
        CONCAT('{', COALESCE(d.datetime_values, ''), '}') as datetime_fields,
        CONCAT('{', COALESCE(t.text_values, ''), '}') as text_fields,
        (
        SELECT CONCAT('{',
        GROUP_CONCAT(
        CONCAT('"', field_id, '":', choices)
        SEPARATOR ','
        ),
        '}')
        FROM selection_fields s2
        WHERE s2.project_id = i.project_id
        AND s2.item_id = i.item_id
        ) as selection_fields
        FROM base_item i
        LEFT JOIN datetime_fields d ON i.project_id = d.project_id AND i.item_id = d.item_id
        LEFT JOIN text_fields t ON i.project_id = t.project_id AND i.item_id = t.item_id
    </select>

</mapper>
