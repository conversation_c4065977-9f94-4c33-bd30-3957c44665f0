package org.dromara.servicetrack.controller;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectAccountTypeBo;
import org.dromara.common.servicetrack.domain.vo.ListViewFieldVo;
import org.dromara.common.servicetrack.domain.vo.ProjectAccountTypeVo;
import org.dromara.common.servicetrack.domain.vo.WorkflowTransitionStateVo;
import org.dromara.common.servicetrack.service.IProjectAccountTypeService;
import org.dromara.common.web.core.BaseController;
import org.dromara.servicetrack.domain.bo.ItemInfoBo;
import org.dromara.servicetrack.domain.bo.ItemListBo;
import org.dromara.servicetrack.domain.vo.ItemInfoVo;
import org.dromara.servicetrack.domain.vo.ItemListVo;
import org.dromara.servicetrack.domain.vo.ItemTempInfoListVo;
import org.dromara.servicetrack.service.IItemInfoService;
import org.dromara.servicetrack.service.IItemTempInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目账户管理
 *
 * <AUTHOR> fei
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/projectAccountType")
public class ProjectAccountTypeController extends BaseController {
    private final IProjectAccountTypeService projectAccountTypeService;

    /**
     * 获取ID项目账户详情
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<ProjectAccountTypeVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(projectAccountTypeService.queryById(id));
    }
    /**
     * 根据Project Id获得项目账户详情
     */
    @GetMapping("/getInfo")
    public R<ProjectAccountTypeVo> getItemInfo(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        @NotNull(message = "AccountTypeId不能为空") @RequestParam Integer accountTypeId) {
        return R.ok(projectAccountTypeService.selectProjectAccountTypeById(projectId,accountTypeId));
    }
    /**
     * 查询项目账户列表
     */
    @GetMapping("/list")
    public R<List<ProjectAccountTypeVo>> list(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId) {
        ProjectAccountTypeBo bo = new ProjectAccountTypeBo();
        bo.setProjectId(projectId);
        return R.ok(projectAccountTypeService.queryList(bo));
    }
    /**
     * 新增条目
     */
    @Log(title = "项目账户管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Integer> add(@Validated @RequestBody ProjectAccountTypeBo bo) {
        return R.ok(projectAccountTypeService.insertByBo(bo));
    }

    /**
     * 修改条目
     */
    @Log(title = "项目账户管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Integer> edit(@Validated @RequestBody ProjectAccountTypeBo bo) {
        return R.ok(projectAccountTypeService.updateByBo(bo));
    }

    /**
     * 删除条目
     *
     * @param ids 主键串
     */
    @Log(title = "项目账户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectAccountTypeService.deleteWithValidByIds(List.of(ids), true));
    }


}
