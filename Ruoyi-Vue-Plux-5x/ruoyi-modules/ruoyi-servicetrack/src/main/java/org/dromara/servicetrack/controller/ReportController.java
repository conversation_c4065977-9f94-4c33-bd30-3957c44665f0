package org.dromara.servicetrack.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.servicetrack.domain.bo.ItemToDoListBo;
import org.dromara.servicetrack.domain.vo.ItemListVo;
import org.dromara.servicetrack.domain.vo.ItemToDoListVo;
import org.dromara.servicetrack.service.IItemInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 报表管理
 *
 * <AUTHOR> fei
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/report")
public class ReportController extends BaseController {
    private final IItemInfoService itemInfoService;

    /**
     * 查询todo列表
     */
    @GetMapping("/todolist")
    public R<ItemToDoListVo> getToDolist(ItemToDoListBo bo, PageQuery pageQuery) {
        return R.ok(itemInfoService.selectPageItemToDoList(bo, pageQuery));
    }
}
