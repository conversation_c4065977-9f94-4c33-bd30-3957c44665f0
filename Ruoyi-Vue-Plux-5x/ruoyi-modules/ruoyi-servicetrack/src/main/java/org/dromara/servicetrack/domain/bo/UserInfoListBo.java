package org.dromara.servicetrack.domain.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.servicetrack.domain.UserInfo;

import java.util.List;

/**
 * 用户信息业务对象
 *  * <AUTHOR> fei
 */

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserInfo.class)
public class UserInfoListBo extends STBaseEntity {

    /**
     * 项目ID
     */
    private Integer projectId;
    /**
     * 关键字查询
     */
    private String keyword;
    /**
     * 排序字段ID：positiveInt：升序；negativeInt：降序
     */
    private Integer sortFieldId;

    /**
     * 显示的用户字段ID
     */
    private List<Integer> fieldIds;
    @JsonIgnore
    private String sqlSegment;
}
