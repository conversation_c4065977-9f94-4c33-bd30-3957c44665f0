package org.dromara.servicetrack.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.servicetrack.constant.*;
import org.dromara.common.servicetrack.domain.bo.ProjectSystemFieldBo;
import org.dromara.common.servicetrack.domain.vo.ListViewFieldVo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldVo;
import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.logic.fieldvalue.FieldValueHandler;
import org.dromara.common.servicetrack.logic.helper.FieldIdHelper;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.service.IProjectSystemFieldService;
import org.dromara.servicetrack.domain.*;
import org.dromara.servicetrack.domain.bo.*;
import org.dromara.servicetrack.domain.vo.*;
import org.dromara.servicetrack.logic.BaseLogic;
import org.dromara.servicetrack.logic.UserInfoLogic;
import org.dromara.servicetrack.mapper.*;
import org.dromara.servicetrack.service.IProjectSettingService;
import org.dromara.servicetrack.service.IUserInfoService;
import org.dromara.servicetrack.service.IUserSettingService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 *  用户管理 服务层实现
 *
 * <AUTHOR> Fei
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserInfoServiceImpl implements IUserInfoService {
    private final UserInfoMapper userInfoMapper;
    private final STUserInfoMapper stuserInfoMapper;
    private final TableSequenceManager tableSequenceManager;
    private final UserInfoTextMapper userInfoTextMapper;
    private final UserInfoSelectionMapper userInfoSelectionMapper;
    private final UserInfoDateTimeMapper userInfoDateTimeMapper;
    private final UserInfoChangelogMapper userInfoChangelogMapper;
    private final UserInfoChangelogFieldMapper userInfoChangelogFieldMapper;
    private final UserInfoChangeTextMapper userInfoChangeTextMapper;
    private final IProjectSettingService projectSettingService;
    private final IProjectSystemFieldService projectSystemFieldService;
    private final IUserSettingService userSettingService;

    @Override
    public TableDataInfo<UserInfoListVo> selectPageUserList(UserInfoListBo bo, PageQuery pageQuery) {
        if( bo.getFieldIds() == null || bo.getFieldIds().isEmpty()){
            List<Integer> defaultSystemFieldIds = eUserSystemFieldDef.getSystemFieldIds();
            bo.setFieldIds(defaultSystemFieldIds);
        }
        List<Integer> textFieldIds = new ArrayList<>();
        List<Integer> dateTimeFieldIds = new ArrayList<>();
        List<Integer> selectionFieldIds = new ArrayList<>();
        if( bo.getFieldIds() != null){
            var customFieldIds = bo.getFieldIds().stream().filter(fieldId -> !FieldIdHelper.IsUserInfoSystemField(fieldId)).toList();
            var pageFields = ProjectManager.getInstance(bo.getProjectId()).getProjectPageFields();

            for(var fieldId:customFieldIds){
                var field = pageFields.stream().filter(f -> f.getFieldId().equals(fieldId)).findFirst().orElse(null);
                if( field == null){
                    continue;
                }
                if( field.getFieldType() == eFieldTypeDef.ShortText.getValue()){
                    textFieldIds.add(fieldId);
                }else if( field.getFieldType() == eFieldTypeDef.Date.getValue()){
                    dateTimeFieldIds.add(fieldId);
                }else if( field.getFieldType() == eFieldTypeDef.Dropdown.getValue() ||
                    field.getFieldType() == eFieldTypeDef.MultipleSelection.getValue() ||
                    field.getFieldType() == eFieldTypeDef.CheckBox.getValue() ||
                    field.getFieldType() == eFieldTypeDef.RadioBox.getValue()){
                    selectionFieldIds.add(fieldId);
                }
            }
        }
        var wrapper = this.buildQueryWrapper(bo);
        this.buildWrapperSortField(bo, wrapper);
        Integer projectId = bo.getProjectId();
        Page<UserInfoListVo> page = userInfoMapper.selectPageUserList(pageQuery.build(), wrapper, projectId,bo.getSqlSegment(), bo.getSortFieldId(),
                                                                 textFieldIds, dateTimeFieldIds, selectionFieldIds);

        List<Integer> systemFieldIds = bo.getFieldIds() != null ? bo.getFieldIds().stream().filter(eUserSystemFieldDef::IsSystemField).toList() : new ArrayList<>();
        List<Integer> customFieldIds = bo.getFieldIds() == null ? Collections.emptyList() : bo.getFieldIds().stream().filter(fieldId -> !FieldIdHelper.IsUserInfoSystemField(fieldId)).toList();
        for (UserInfoListVo userInfoVo : page.getRecords()){
            List<ListFieldVo> fields = new ArrayList<>();
            getSystemFieldValues(userInfoVo, fields, systemFieldIds);
            getCustomFieldValues(userInfoVo, projectId,fields, customFieldIds);
            userInfoVo.setValues(fields);
        }
        return TableDataInfo.build(page);
    }

    @Override
    public STUserInfoVo getUserInfoDetail(Integer projectId, Integer userId) {
        if(projectId == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if(userId == 0){
            throw new ServiceException("user Id can't be 0");
        }
        STUserInfoVo detail = userInfoMapper.getUserInfoDetail(projectId, userId);
        if(detail == null){
            throw  new ServiceException(String.format("User(%d-%d) can't not found",projectId,userId));
        }
        try{
            detail.parseCustomFields();
        }
        catch (Exception e) {
            log.error("Error parsing custom fields for user {}: {}", userId, e.getMessage());
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        int curLoginUserId = loginUser != null ? loginUser.getExternalUserId():0;
        if( detail.getProjectId() == null || detail.getProjectId() == 0)
            detail.setProjectId(projectId);
        UserInfoLogic userInfoLogic = new UserInfoLogic(tableSequenceManager, curLoginUserId, detail);
        userInfoLogic.retrieveUserInfoFieldValues();

        //get change logs
//        List<UserInfoChangelogVo> changelogs = userInfoChangelogMapper.selectVoList(new LambdaQueryWrapper<>(UserInfoChangelog.class)
//            .eq(UserInfoChangelog::getProjectId, projectId)
//            .eq(UserInfoChangelog::getUserId,userId)
//            .orderByDesc(UserInfoChangelog::getLogTime));
//
//        if( changelogs != null){
//            for (UserInfoChangelogVo changelog : changelogs) {
//                changelog.parseDescription4MultiLang(eCultureCode.ZH_CN);
//                changelog.setModifiedByName(ProjectManager.getInstance(stConstant.System_Project_Id).getSysUserName(changelog.getModifiedById()));
//            }
//            detail.setChangelogs(changelogs);
//        }

        return detail;
    }

    @Override
    public Integer insertByBo(UserInfoBo bo) {
        if(bo.getProjectId() == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if( bo.getFields() == null || bo.getFields().isEmpty()){
            throw new ServiceException("user fields are empty, so can't create user.");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        int curLoginUserId = loginUser != null ? loginUser.getExternalUserId():0;
        UserInfoLogic userInfoLogic = new UserInfoLogic(tableSequenceManager, curLoginUserId, null);
        UserInfo add = userInfoLogic.ConvertToUserInfo(bo);
        boolean flag = userInfoMapper.insert(add) > 0;
        if (flag) {
            bo.setUserId(add.getUserId());
            if( bo.getStUserInfo() != null){
                STUserInfo stuserInfo = MapstructUtils.convert(bo.getStUserInfo(), STUserInfo.class);
                stuserInfoMapper.insert(stuserInfo);
            }
            //update user info other fields
            insertUserInfoOtherFields(bo,false);

            //insert user info change log
            insertUserInfoChangeLogs(bo);
        }
        return flag ? bo.getExternalUserId():0;
    }
    private void insertUserInfoOtherFields(UserInfoBo bo, boolean clear) {
        if(bo.getTexts() != null && !bo.getTexts().isEmpty()){
            if( clear){
                var fieldIds = bo.getTexts().stream().map(BaseFieldBo::getFieldId).toList();
                userInfoTextMapper.delete(new LambdaQueryWrapper<UserInfoText>()
                    .eq(UserInfoText::getProjectId, bo.getProjectId())
                    .eq(UserInfoText::getUserId, bo.getExternalUserId())
                    .in(UserInfoText::getFieldId, fieldIds));
            }
            List<UserInfoText> list = new ArrayList<UserInfoText>();;
            for(var text:bo.getTexts()){
                UserInfoText oneItemText = MapstructUtils.convert(text, UserInfoText.class);
                list.add(oneItemText);
            }
            userInfoTextMapper.insertBatch(list);
        }
        if(bo.getSelections() != null && !bo.getSelections().isEmpty()){
            if( clear){
                var fieldIds = bo.getSelections().stream().map(BaseFieldBo::getFieldId).toList();
                userInfoSelectionMapper.delete(new LambdaQueryWrapper<UserInfoSelection>()
                    .eq(UserInfoSelection::getProjectId, bo.getProjectId())
                    .eq(UserInfoSelection::getUserId, bo.getExternalUserId())
                    .in(UserInfoSelection::getFieldId, fieldIds));
            }
            List<UserInfoSelection> list = new ArrayList<UserInfoSelection>();
            for(var selection:bo.getSelections()){
                UserInfoSelection oneItemSelection = MapstructUtils.convert(selection, UserInfoSelection.class);
                list.add(oneItemSelection);
            }
            userInfoSelectionMapper.insertBatch(list);
        }
        if(bo.getDateTimes() != null && !bo.getDateTimes().isEmpty()){
            if( clear){
                var fieldIds = bo.getDateTimes().stream().map(BaseFieldBo::getFieldId).toList();
                userInfoDateTimeMapper.delete(new LambdaQueryWrapper<UserInfoDateTime>()
                    .eq(UserInfoDateTime::getProjectId, bo.getProjectId())
                    .eq(UserInfoDateTime::getUserId, bo.getExternalUserId())
                    .in(UserInfoDateTime::getFieldId, fieldIds));
            }
            List<UserInfoDateTime> list = new ArrayList<UserInfoDateTime>();
            for(var dateTime:bo.getDateTimes()){
                UserInfoDateTime oneItemDateTime = MapstructUtils.convert(dateTime, UserInfoDateTime.class);
                list.add(oneItemDateTime);
            }
            userInfoDateTimeMapper.insertBatch(list);
        }
    }
    private  void insertUserInfoChangeLogs(UserInfoBo bo){
        if(bo.getChangelog() != null){
            UserInfoChangelog userInfochangelog = MapstructUtils.convert(bo.getChangelog(), UserInfoChangelog.class);
            if(userInfochangelog != null){
                userInfochangelog.setProjectId(bo.getProjectId());
                userInfoChangelogMapper.insert(userInfochangelog);
            }

            if(bo.getChangelog().getChangelogFields() != null && !bo.getChangelog().getChangelogFields().isEmpty()){
                List<UserInfoChangelogField> list = new ArrayList<UserInfoChangelogField>();
                for (var changelogField : bo.getChangelog().getChangelogFields()) {
                    UserInfoChangelogField userInfochangelogField = MapstructUtils.convert(changelogField, UserInfoChangelogField.class);
                    if(userInfochangelogField != null) {
                        userInfochangelogField.setChangelogId(changelogField.getChangelogId());
                        list.add(userInfochangelogField);
                    }
                }
                userInfoChangelogFieldMapper.insertBatch(list);
            }
        }
        if(bo.getChangeTexts() != null && !bo.getChangeTexts().isEmpty()){
            List<UserInfoChangeText> list = new ArrayList<UserInfoChangeText>();
            for(var changeText:bo.getChangeTexts()){
                UserInfoChangeText oneChangeText = MapstructUtils.convert(changeText, UserInfoChangeText.class);
                list.add(oneChangeText);
            }
            userInfoChangeTextMapper.insertBatch(list);
        }
    }
    @Override
    public Integer updateByBo(UserInfoBo bo) {
        if(bo.getProjectId() == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if(bo.getExternalUserId() == 0){
            throw new ServiceException("User Id can't be 0");
        }
        if( bo.getFields() == null || bo.getFields().isEmpty()){
            throw new ServiceException("User fields are empty, so don't need to update.");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        int curLogicUserId = loginUser != null ? loginUser.getExternalUserId():0;
        var userInfoVo = getUserInfoDetail(bo.getProjectId(), bo.getExternalUserId());
        if(userInfoVo == null){
            throw new ServiceException(String.format("User(%d-%d) can't not found",bo.getProjectId(),bo.getExternalUserId()));
        }
        UserInfoLogic userInfoLogic = new UserInfoLogic(tableSequenceManager, curLogicUserId, userInfoVo);
        if( bo.getUserId() == null ||  bo.getUserId() == 0)
            bo.setUserId(userInfoVo.getUserId());
        UserInfo update = userInfoLogic.ConvertToUserInfo(bo);
        boolean flag = userInfoMapper.updateById(update) > 0;
        if (flag) {
            if( bo.getStUserInfo() != null){
                STUserInfo updateSTUserInfo = MapstructUtils.convert(bo.getStUserInfo(), STUserInfo.class);
               var stUserInfo = stuserInfoMapper.selectOne(new LambdaQueryWrapper<STUserInfo>().eq(STUserInfo::getUserId,bo.getExternalUserId()));
               boolean isUpdated = false;
               if(stUserInfo != null){
                  bo.getStUserInfo().setId(stUserInfo.getId());
                  isUpdated = true;
               }
               if( isUpdated)
                    stuserInfoMapper.updateById(updateSTUserInfo);
               else
                   stuserInfoMapper.insert(updateSTUserInfo);
            }
            //update user info other fields
            insertUserInfoOtherFields(bo,true);

            //insert user info change log
            insertUserInfoChangeLogs(bo);
        }
        return flag ? bo.getExternalUserId():0;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Integer projectId, Boolean isValid) {

        var userInfos = userInfoMapper.selectVoByIds(ids);
        if (userInfos == null || userInfos.isEmpty()) {
            return false;
        }
        var externalUserIds = userInfos.stream().map(STUserInfoVo::getExternalUserId).toList();
        return null;
    }

    @Override
    public ListViewFieldVo getListviewFields(Integer projectId, Integer option) {
        ListViewFieldVo vo = new ListViewFieldVo();
        var loginUser = LoginHelper.getLoginUser();
        if( loginUser == null)
            throw new ServiceException("登录用户不存在!");
        var userId = loginUser.getExternalUserId();
        List<Integer> userSettingIds = new ArrayList<>();
        userSettingIds.add(eUserSetting.ListView_SelectedColumns.getValue());
        var userSelectedFields = userSettingService.selectUserSettingList(projectId, userId,userSettingIds);
        List<Integer> selectedFieldIds = FieldIdHelper.getDefaultUserListViewFieldIds();

        if( userSelectedFields != null && !userSelectedFields.isEmpty())
        {
            var strSelectedFields = userSelectedFields.get(0).getSettingContent();
            if( strSelectedFields != null && !strSelectedFields.isEmpty())
            {
                selectedFieldIds =  StringUtils.splitTo(strSelectedFields, Convert::toInt);
            }
        }
        ProjectSystemFieldBo bo = new ProjectSystemFieldBo();
        bo.setProjectId(projectId);
        bo.setModuleId(eSTModuleIDDef.UserInfo.getValue());
        var allFields = projectSystemFieldService.selectFieldList(bo);
        var pageFields = ProjectManager.getInstance(projectId).getProjectPageFields();

        return BaseLogic.getListviewFields(allFields, pageFields, selectedFieldIds, eSTModuleIDDef.UserInfo, option);
    }

    private void getSystemFieldValues(UserInfoListVo userInfoVo, List<ListFieldVo> fields, List<Integer> systemFieldIds) {
        if(  userInfoVo == null || systemFieldIds == null || systemFieldIds.isEmpty() )
            return;

        for(var fieldId: systemFieldIds) {
            switch (eUserSystemFieldDef.from(fieldId)) {
                case UserId: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(userInfoVo.getExternalUserId().toString());
                        }
                    });
                    break;
                }
                case UserName: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(userInfoVo.getUserName());
                        }
                    });
                    break;
                }
                case NickName: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(userInfoVo.getNickName());
                        }
                    });
                    break;
                }
                case Email: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(userInfoVo.getEmail());
                        }
                    });
                    break;
                }
                case Phone: {
                    fields.add( new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(userInfoVo.getPhoneNumber());
                        }
                    });
                    break;
                }
                case Status: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(userInfoVo.getStatus() == 1 ? "1" : "0");
                        }
                    });
                    break;
                }
                case Depart: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(userInfoVo.getDeptName());
                        }
                    });
                    break;
                }
                case CreatedTime: {
                    String createdTime = DateUtils.dateTime(userInfoVo.getCreateTime());
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(createdTime);
                        }
                    });
                    break;
                }
                case CreatedBy: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(ProjectManager.getInstance(stConstant.System_Project_Id).getSysUserNickName(userInfoVo.getCreateBy()));
                        }
                    });
                    break;
                }
            }
        }
    }
    private void getCustomFieldValues(UserInfoListVo userInfoVo , Integer projectId, List<ListFieldVo> fields, List<Integer> customFieldIds){
        if(  userInfoVo == null || customFieldIds == null || customFieldIds.isEmpty() )
            return;

        try {
            userInfoVo.parseCustomFields();

            Map<String, String> textMap = userInfoVo.getTextFields();
            if( textMap != null && !textMap.isEmpty()){
                for( var entry : textMap.entrySet()){
                    int fieldId = Integer.parseInt(entry.getKey());
                    fields.add(new ListFieldVo(){
                        {
                            setId(fieldId);
                            setValue(entry.getValue());
                        }
                    });
                }
            }
            var selectionMap = userInfoVo.getSelectionFields();
            if( selectionMap != null && !selectionMap.isEmpty()){
                for( var entry : selectionMap.entrySet()){
                    int fieldId = Integer.parseInt(entry.getKey());
                    FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId,eSTModuleIDDef.Incident, fieldId,0);
                    IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
                    oldFieldValue.readValueFromDB(entry.getValue());
                    fields.add(new ListFieldVo(){
                        {
                            setId(fieldId);
                            setValue(oldFieldValue.getDisplayValue());
                        }
                    });
                }
            }
            var dateTimeMap = userInfoVo.getDatetimeFields();
            if( dateTimeMap != null && !dateTimeMap.isEmpty()){
                for( var entry : dateTimeMap.entrySet()) {
                    int fieldId = Integer.parseInt(entry.getKey());
                    FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId,eSTModuleIDDef.Incident, fieldId,0);
                    IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
                    oldFieldValue.readValueFromDB(entry.getValue());
                    fields.add(new ListFieldVo(){
                        {
                            setId(fieldId);
                            setValue(oldFieldValue.getDisplayValue());
                        }
                    });
                }
            }
        }
        catch (Exception e) {
            log.error("parseCustomFields in user's getCustomFieldValues error",e);
        }
    }
    private void buildWrapperSortField(UserInfoListBo bo, QueryWrapper<UserInfo> wrapper){
        //sort field
        if(bo.getSortFieldId() != null && bo.getSortFieldId() != 0){
            boolean asc = bo.getSortFieldId() > 0;
            var pageFields = ProjectManager.getInstance(bo.getProjectId()).getProjectPageFields();

            var sortFieldId = Math.abs(bo.getSortFieldId());
            String sortFieldName = "";
            if(FieldIdHelper.IsUserInfoSystemField(sortFieldId)){
                if( sortFieldId == eUserSystemFieldDef.UserId.getValue()){
                    sortFieldName = "u.external_user_id";
                }
                else if( sortFieldId == eUserSystemFieldDef.NickName.getValue()){
                    sortFieldName = "u.nick_name";
                }
                else if( sortFieldId == eUserSystemFieldDef.UserName.getValue()) {
                    sortFieldName = "u.user_name";
                }
                else if( sortFieldId == eUserSystemFieldDef.Status.getValue()) {
                    sortFieldName = "u.status";
                }
                else if( sortFieldId == eUserSystemFieldDef.Email.getValue()) {
                    sortFieldName = "u.email";
                }
                else if( sortFieldId == eUserSystemFieldDef.Phone.getValue()) {
                    sortFieldName = "u.phonenumber";
                }
                else if( sortFieldId == eUserSystemFieldDef.CreatedTime.getValue()) {
                    sortFieldName = "u.create_time";
                }
                else if( sortFieldId == eUserSystemFieldDef.CreatedBy.getValue()) {
                    sortFieldName = "submittedUser.nick_name";
                }
                else if( sortFieldId == eUserSystemFieldDef.Depart.getValue()) {
                    sortFieldName = "d.dept_name";
                }

            }  else if( FieldIdHelper.IsCustomField(sortFieldId)){
                var field = pageFields.stream().filter(f -> f.getFieldId().equals(sortFieldId)).findFirst().orElse(null);
                if( field != null) {
                    if (field.getFieldType() == eFieldTypeDef.ShortText.getValue()) {
                        sortFieldName = "t.min_text";
                        bo.setSortFieldId(stConstant.ListView_SortField_Text);
                    } else if (field.getFieldType() == eFieldTypeDef.Date.getValue()) {
                        sortFieldName = "d.min_datetime";
                        bo.setSortFieldId(stConstant.ListView_SortField_DateTime);
                    } else if (field.getFieldType() == eFieldTypeDef.Dropdown.getValue() ||
                        field.getFieldType() == eFieldTypeDef.MultipleSelection.getValue() ||
                        field.getFieldType() == eFieldTypeDef.CheckBox.getValue() ||
                        field.getFieldType() == eFieldTypeDef.RadioBox.getValue()) {
                        sortFieldName = "fs.choice_name";
                        bo.setSortFieldId(stConstant.ListView_SortField_Selection);
                    }
                }
            }
            if(!sortFieldName.isEmpty())
                wrapper = asc ? wrapper.orderByAsc(sortFieldName) : wrapper.orderByDesc(sortFieldName);
        }
    }
    private QueryWrapper<UserInfo> buildQueryWrapper(UserInfoListBo bo) {
        QueryWrapper<UserInfo> wrapper = Wrappers.query();
        List<Object> paramValues = new ArrayList<>();

        //just service trick user
        wrapper.ge("u.st_user_type", 1);
        paramValues.add(1);
        wrapper.eq( "u.del_flag", 0);
        paramValues.add(0);
        if(bo.getKeyword() != null && !bo.getKeyword().isEmpty()){

            var keyword = bo.getKeyword().trim();
            { // Handle nick_name or user_name queries
                String[] names = keyword.split(",");
                Integer index = 0;
                for (String name : names) {
                   final String processedName = name.replaceAll("(\"|“)(.+?)(\"|”)", "$2").trim();
                    if (index > 0) {
                        wrapper.or(w -> w.like("u.user_name", processedName)
                            .or()
                            .like("u.nick_name", processedName));
                    } else {
                        wrapper.and(w -> w.like("u.user_name", processedName)
                            .or()
                            .like("u.nick_name", processedName));
                    }
                    paramValues.add("'%" + name + "%'"); // 记录参数值
                    paramValues.add("'%" + name + "%'"); // 为nick_name添加一个参数值
                    index++;
                }
            }
        }
        // 获取完整 SQL
        String targetSql = wrapper.getTargetSql();

        // 替换占位符
        for (Object value : paramValues) {
            if (value instanceof List) { // 处理数组值
                List<?> listValue = (List<?>) value;
                for (Object listItem : listValue) {
                    targetSql = targetSql.replaceFirst("\\?", listItem.toString());
                }
            } else { // 处理单个值
                targetSql = targetSql.replaceFirst("\\?", value.toString());
            }
        }
        //just get item's sql segment for query, if there are any other table join, please keep just get user level sql segment
        bo.setSqlSegment(targetSql);
        System.out.println("Generated SQL: " + targetSql);
        return wrapper;
    }
}
