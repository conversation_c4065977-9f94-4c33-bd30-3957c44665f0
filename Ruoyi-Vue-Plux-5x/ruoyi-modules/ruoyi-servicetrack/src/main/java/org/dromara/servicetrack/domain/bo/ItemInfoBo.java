package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.servicetrack.domain.ItemChangeText;
import org.dromara.servicetrack.domain.ItemInfo;
import org.dromara.servicetrack.model.field.FieldValue;
import org.dromara.common.servicetrack.domain.bo.FieldValueBo;
import java.util.Date;
import java.util.List;

/**
 * 条目业务对象 item_info
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemInfo.class)
public class ItemInfoBo  extends BaseInfoBo {
    /**
     * ID
     */
    //@NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    @Min(value = 1, message = "projectId 必须大于 0")
    private Integer projectId;

    /**
     * 工作项目ID
     */
    private Integer workProjectId;
    /**
     * 条目ID
     */
    @NotNull(message = "条目ID不能为空")
    private Integer itemId;

    /**
     * 条目预分配ID
     */
    private Integer  preAllocatedItemId;
    /**
     * 条目名称
     */
    @NotNull(message = "条目标题不能为空", groups = { AddGroup.class })
    @Xss(message = "条目标题不能包含脚本字符",groups = { AddGroup.class})
    @NotBlank(message = "条目标题不能为空",groups = { AddGroup.class})
    @Size(min = 0, max = 500, message = "条目标题长度不能超过{max}个字符",groups = { AddGroup.class})
    private String itemTitle;

    /**
     * 显示ID
     */
    private String displayId;

    /**
     * 条目拥有者ID
     */
    private Integer ownerId;

    /**
     * 条目状态ID
     */
    private Integer stateId;

    /**
     * 模块ID
     */
    private Integer moduleId;

    /**
     * 条目类型ID
     */
    private Integer typeId;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 创建者
     */
    private Integer createdBy;

    /**
     * 修改时间
     */
    private Date modifiedTime;

    /**
     *  修改者
     */
    private Integer modifiedBy;

    /**
     * 分配时间
     */
    private Date assignedTime;

//    /**
//     * field values
//     */
//    private List<FieldValueBo> fields;
//
//    /**
//     * 条目文本
//     */
//    private List<ItemTextBo> texts;
//
//    /**
//     * 条目时间
//     */
//    private List<ItemDateTimeBo> dateTimes;
//
//    /**
//     * 条目选择
//     */
//    private List<ItemSelectionBo> selections;
//
//    /**
//     * 条目日志
//     */
//    private List<ItemChangelogBo> changelogs;
//
//    /**
//     * 条目变更文本
//     */
//    private List<ItemChangeTextBo> changeTexts;
    /**
     * 条目历史
     */
    private  ItemHistoryBo historyBo;
}
