package org.dromara.servicetrack.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.List;

/**
 * Report todo 列表业务对象
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ItemToDoListBo extends STBaseEntity {
    @NotNull(message = "项目ID不能为空")
    private List<Integer> projectIds;
    private Integer ownerId;
    private Integer stateId;
    private Integer sortFieldId;
}
