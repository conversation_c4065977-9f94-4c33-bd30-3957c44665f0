package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.UserInfoDateTime;

import java.util.Date;


/**
 * 业务对象 user_info_datetime
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserInfoDateTime.class)
public class UserInfoDateTimeBo extends UserInfoFieldBo{
    /**
     * date time
     */
    private Date dateTime;
}
