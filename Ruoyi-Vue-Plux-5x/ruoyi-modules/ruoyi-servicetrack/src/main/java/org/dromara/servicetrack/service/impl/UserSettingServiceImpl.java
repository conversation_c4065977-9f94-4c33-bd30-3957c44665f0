package org.dromara.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.servicetrack.domain.ProjectSetting;
import org.dromara.servicetrack.domain.UserSetting;
import org.dromara.servicetrack.domain.bo.ProjectSettingBo;
import org.dromara.servicetrack.domain.bo.UserSettingBinderBo;
import org.dromara.servicetrack.domain.bo.UserSettingBo;
import org.dromara.servicetrack.domain.vo.UserSettingVo;
import org.dromara.servicetrack.mapper.UserSettingMapper;
import org.dromara.servicetrack.service.IUserSettingService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@RequiredArgsConstructor
@Service
public class UserSettingServiceImpl implements IUserSettingService {
   private final UserSettingMapper userSettingMapper;

    @Override
    public List<UserSettingVo> selectUserSettingList(Integer projectId, Integer userId,List<Integer> settingIds) {
        if(settingIds == null || settingIds.isEmpty()){
            throw new ServiceException("settingIds is null or empty");
        }
        UserSettingBo bo = new UserSettingBo();
        bo.setProjectId(projectId);
        bo.setUserId(userId);
        QueryWrapper<UserSetting> wrapper = Wrappers.query();
        wrapper.eq(bo.getProjectId() != null, "project_id", bo.getProjectId());
        wrapper.eq(bo.getUserId() != null, "user_id", bo.getUserId());
        wrapper.in(true, "setting_id",settingIds);
        return userSettingMapper.selectVoList(wrapper);
    }


    @Override
    public Boolean updateUserSetting(UserSettingBinderBo bo) {
        if (bo.getSettingLst() == null || bo.getSettingLst().isEmpty()) {
            return false;
        }
        var settingIds = bo.getSettingLst().stream().map(UserSettingBinderBo.SettingInfoBo::getSettingId).toList();
        var projectId = bo.getProjectId();
        var userId = bo.getUserId();
        var settingListVo = selectUserSettingList(projectId, userId, settingIds);
        List<UserSetting> insertList = new ArrayList<>();
        List<UserSetting> updateList = new ArrayList<>();
        for (var setting : bo.getSettingLst()) {
            var settingId = setting.getSettingId();
            if (settingListVo.stream().noneMatch(s -> s.getSettingId().equals(settingId))) {
                //need to insert
                var userSetting =  bo.convertToUserSetting(setting,null);
                insertList.add(userSetting);
            }
            else{
                //need to update
                var settingVo = settingListVo.stream().filter(s -> s.getSettingId().equals(settingId)).findFirst();
                if (settingVo.isPresent()) {
                    var userSetting = bo.convertToUserSetting(setting, settingVo.get().getId());
                    updateList.add(userSetting);
                }
            }
        }
        if (!insertList.isEmpty()) {
            userSettingMapper.insertBatch(insertList);
        }
        if (!updateList.isEmpty()) {
            userSettingMapper.updateBatchById(updateList);
        }
        return true;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        return userSettingMapper.deleteByIds(keyIds) > 0;
    }
}
