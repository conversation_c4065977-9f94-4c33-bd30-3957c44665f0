package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.UserInfoChangelog;

/**
 * 用户日志对象 user_info_changelog
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserInfoChangelog.class)
public class UserInfoChangelogBo extends BaseChangelogBo{
    /**
     * user id
     */
    private Integer userId;
}
