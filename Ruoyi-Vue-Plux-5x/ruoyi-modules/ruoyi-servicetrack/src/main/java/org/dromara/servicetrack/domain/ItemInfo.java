package org.dromara.servicetrack.domain;
import com.baomidou.mybatisplus.annotation.*;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
/**
 *  Item_Info
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("item_info")
public class ItemInfo extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * item id
     */
    @TableField(value = "item_id")
    private Integer itemId;

    /**
     * module id
     */
    @TableField(value = "module_id")
    private Integer moduleId;

    /**
     * item display id
     */
    @TableField(value = "display_id")
    private String displayId;

    /**
     * item title
     */
    @TableField(value = "item_title")
    private String itemTitle;

    /**
     * owner id
     */
    @TableField(value = "owner_id")
    private Integer ownerId;

    /**
     * state id
     */
    @TableField(value = "state_id")
    private Integer stateId;

    /**
     * item type id
     */
    @TableField(value = "type_id")
    private Integer typeId;

    /**
     * created time
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * created by
     */
    @TableField(value = "created_by")
    private Integer createdBy;

    /**
     * modified time
     */
    @TableField(value = "modified_time")
    private Date modifiedTime;

    /**
     * modified by
     */
    @TableField(value = "modified_by")
    private Integer modifiedBy;

    /**
     * assigned time
     */
    @TableField(value = "assigned_time")
    private Date assignedTime;

    /**
     * contact id
     */
    @TableField(value = "contact_id")
    private Integer contactId;
}
