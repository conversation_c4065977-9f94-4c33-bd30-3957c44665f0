package org.dromara.servicetrack.logic;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.servicetrack.domain.vo.ProjectMemberVo;
import org.dromara.common.servicetrack.domain.vo.WorkflowStateVo;
import org.dromara.common.servicetrack.domain.vo.WorkflowTransitionVo;
import org.dromara.common.servicetrack.logic.project.ProjectManager;

import java.util.List;

/**
 * 条目业务对象 item logic
 *
 * <AUTHOR> fei
 */
@Slf4j
public class ProjectLogic extends BaseLogic{

    private final Integer ProjectId;
    private final ProjectManager projectManager;
    public ProjectLogic(Integer ProjectId, Integer UserId) {
        super(UserId);
        this.ProjectId = ProjectId;
        projectManager = ProjectManager.getInstance(ProjectId);
    }

    /**
     * 获取项目成员列表
     *
     */
    public List<ProjectMemberVo> getMemberList(){
        return  projectManager.getProjectMembers();
    }

    /**
     * 获取项目 状态列表
     *
     */
    public List<WorkflowStateVo> getStateList(){
        return projectManager.getWorkflowStates();
    }
    /**
     * 获取项目 transition列表
     *
     */
    public List<WorkflowTransitionVo> getTransitionList(){
        return projectManager.getWorkflowTransitions();
    }
    public void resetProjectSetting(){
        projectManager.reset(ProjectId);
    }
}
