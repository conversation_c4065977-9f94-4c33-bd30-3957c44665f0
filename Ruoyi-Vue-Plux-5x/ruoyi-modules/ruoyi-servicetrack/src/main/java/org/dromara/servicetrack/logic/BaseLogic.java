package org.dromara.servicetrack.logic;

import org.dromara.common.servicetrack.constant.eFieldTypeDef;
import org.dromara.common.servicetrack.constant.eSTModuleIDDef;
import org.dromara.common.servicetrack.constant.eSystemFieldDef;
import org.dromara.common.servicetrack.constant.eUserSystemFieldDef;
import org.dromara.common.servicetrack.domain.vo.ListViewFieldVo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldVo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageFieldVo;
import org.dromara.common.servicetrack.logic.helper.FieldIdHelper;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class BaseLogic {
    protected  Integer UserId;
    public BaseLogic(Integer UserId){
        this.UserId = UserId;
    }

    public static ListViewFieldVo getListviewFields(List<ProjectFieldVo> allFields, List<ProjectPageFieldVo> pageFields,
                                                    List<Integer> selectedFieldIds, eSTModuleIDDef moduleId, int option){
        ListViewFieldVo vo = new ListViewFieldVo();
        if(allFields == null || allFields.isEmpty())
            return vo;
        List<ProjectFieldVo> availableFields = new ArrayList<>();
        for(var field:allFields) {
            if( FieldIdHelper.IsUserInfoSystemField(field.getFieldId())) {
                if( moduleId == eSTModuleIDDef.UserInfo) {
                    if (field.getFieldId() == eUserSystemFieldDef.SupportTeam.getValue() || field.getFieldId() == eUserSystemFieldDef.PrimarySupport.getValue() ||
                        field.getFieldId() == eUserSystemFieldDef.SecondarySupport.getValue())
                        continue;
                }
                else if( moduleId == eSTModuleIDDef.Incident) {
                    if( field.getFieldId() == eSystemFieldDef.Description.getValue() || field.getFieldId() == eSystemFieldDef.Solution.getValue())
                        continue;
                }
                availableFields.add(field);
            }
            else {
                if( field.getFieldType() == eFieldTypeDef.RichText.getValue() || field.getFieldType() == eFieldTypeDef.PlainText.getValue()) {
                    continue;
                }
                if( pageFields.stream().anyMatch(p -> Objects.equals(p.getFieldId(), field.getFieldId()))) {
                    availableFields.add(field);
                }
            }
        }
        if( option == 0) {
            vo.setAvailableFields(availableFields.stream()
                .filter(p -> !selectedFieldIds.contains(p.getFieldId()))
                .map(p -> {
                    ProjectFieldVo fieldVo = new ProjectFieldVo();
                    fieldVo.setFieldId(p.getFieldId());
                    fieldVo.setFieldName(p.getFieldName());
                    fieldVo.setFieldType(p.getFieldType());
                    return fieldVo;
                })
                .collect(Collectors.toList()));
        }
        if( option == 0 || option == 1) {
            vo.setSelectedFields(availableFields.stream()
                .filter(p -> selectedFieldIds.contains(p.getFieldId()))
                .map(p -> {
                    ProjectFieldVo fieldVo = new ProjectFieldVo();
                    fieldVo.setFieldId(p.getFieldId());
                    fieldVo.setFieldName(p.getFieldName());
                    fieldVo.setFieldType(p.getFieldType());
                    return fieldVo;
                })
                .sorted(Comparator.comparingInt(p -> selectedFieldIds.indexOf(p.getFieldId())))
                .collect(Collectors.toList()));
        }
        return vo;
    }
}
