package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.servicetrack.domain.ItemDateTime;
import org.dromara.servicetrack.domain.ItemInfo;
import org.dromara.servicetrack.model.field.FieldValue;
import org.dromara.common.servicetrack.domain.bo.FieldValueBo;
import java.util.Date;
import java.util.List;

/**
 * 业务对象 item_datetime
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemDateTime.class)
public class ItemDateTimeBo  extends ItemFieldBo {

    /**
     * 日期时间
     */
    private Date dateTime;
}
