package org.dromara.servicetrack.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.servicetrack.domain.ProjectSetting;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectSettingBinderBo extends STBaseEntity {

    @NotNull(message = "项目ID不能为空")
    protected  Integer projectId;

    protected List<SettingInfoBo> settingLst;
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class SettingInfoBo extends STBaseEntity {

        @NotNull(message = "设置ID不能为空")
        private Integer settingId;

        @NotNull(message = "设置名称不能为空")
        @Xss(message = "设置名称不能包含脚本字符")
        @NotBlank(message = "设置名称不能为空")
        @Size(min = 1, max = 500, message = "设置名称的长度不能超过{max}")
        private String settingName;

        private Integer settingOption;

        private String settingContent;
    }
    public ProjectSetting convertToProjectSetting(SettingInfoBo bo,Long id) {
        ProjectSetting projectSetting = new ProjectSetting();
        projectSetting.setProjectId(this.projectId);
        if( id != null)
            projectSetting.setId(id);

        projectSetting.setSettingId(bo.getSettingId());
        projectSetting.setSettingOption(bo.getSettingOption());
        projectSetting.setSettingName(bo.getSettingName());
        projectSetting.setSettingContent(bo.getSettingContent());

        return projectSetting;
    }
}
