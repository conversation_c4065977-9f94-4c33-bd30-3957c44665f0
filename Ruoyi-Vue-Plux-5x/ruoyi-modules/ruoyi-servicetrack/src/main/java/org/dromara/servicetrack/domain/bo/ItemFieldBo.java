package org.dromara.servicetrack.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * 条目字段对象 item field 基类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ItemFieldBo extends BaseFieldBo {
    /**
     * 条目ID
     */
    @NotNull(message = "条目ID不能为空")
    private Integer itemId;
}
