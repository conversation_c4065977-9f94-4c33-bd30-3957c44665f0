package org.dromara.servicetrack.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.bo.FieldValueBo;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class BaseInfoBo extends STBaseEntity {

    /**
     * field values
     */
    private List<FieldValueBo> fields;

    /**
     * 条目文本
     */
    private List<BaseFieldBo> texts;

    /**
     * 条目时间
     */
    private List<BaseFieldBo> dateTimes;

    /**
     * 条目选择
     */
    private List<BaseFieldBo> selections;

    /**
     * 条目日志
     */
    private BaseChangelogBo changelog;

    /**
     * 条目变更文本
     */
    private List<BaseFieldBo> changeTexts;
}
