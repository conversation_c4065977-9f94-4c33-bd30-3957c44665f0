package org.dromara.servicetrack.logic;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.servicetrack.constant.eSTModuleIDDef;
import org.dromara.common.servicetrack.constant.eSystemFieldDef;
import org.dromara.common.servicetrack.domain.bo.FieldValueBo;
import org.dromara.common.servicetrack.domain.vo.WorkflowTransitionStateVo;
import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.logic.fieldvalue.FieldValueHandler;
import org.dromara.common.servicetrack.logic.fieldvalue.RichTextFieldValue;
import org.dromara.common.servicetrack.logic.helper.FieldIdHelper;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.constant.eFieldTypeDef;
import org.dromara.common.servicetrack.constant.eUserSystemFieldDef;
import org.dromara.common.servicetrack.utils.ValueConvert;
import org.dromara.servicetrack.domain.STUserInfo;
import org.dromara.servicetrack.domain.UserInfo;
import org.dromara.servicetrack.domain.bo.*;
import org.dromara.servicetrack.domain.vo.STUserInfoVo;
import org.dromara.servicetrack.model.field.TFieldValueVo;

import java.util.ArrayList;
import java.util.Date;
import java.util.Map;

/**
 * 用户信息业务逻辑类
 * User info logic
 *
 * <AUTHOR> fei
 */
@Slf4j
public class UserInfoLogic extends AbstractEntityLogic<UserInfoBo, STUserInfoVo> {
    private final STUserInfoVo originalUserInfoVo;

    public UserInfoLogic(TableSequenceManager tableSequenceManager, Integer UserId, STUserInfoVo originalUserInfoVo) {
        super(tableSequenceManager, UserId,originalUserInfoVo,InfoVoType.USER_INFO);
        this.originalUserInfoVo = originalUserInfoVo;
    }

    public UserInfo ConvertToUserInfo(UserInfoBo bo) {
        try {
            if (bo == null) {
                throw new ServiceException("UserInfoBo cannot be null");
            }
            if (bo.getProjectId() == null) {
                throw new ServiceException("ProjectId cannot be null");
            }
            boolean isNew = bo.getExternalUserId() == null || bo.getExternalUserId() == 0;
            int externalUserId = isNew ? tableSequenceManager.getNextSequence(SequenceTable.USER) : bo.getExternalUserId();
            bo.setExternalUserId(externalUserId);

            if (isNew) {
                Date currentTime = DateUtils.getNowDate();
                LoginUser loginUser = LoginHelper.getLoginUser();
                bo.setCreateBy(loginUser != null ? loginUser.getUserId() : 0);
                bo.setCreateTime(currentTime);
            }

            var fieldValues = bo.getFields();
            if (fieldValues != null && !fieldValues.isEmpty()) {
                //remove duplicate fields while keeping the last occurrence of each fieldId
                Map<Integer, FieldValueBo> uniqueFields = new java.util.LinkedHashMap<>();
                for (var field : fieldValues) {
                    uniqueFields.put(field.getFieldId(), field);
                }
                fieldValues.clear();
                fieldValues.addAll(uniqueFields.values());
                var hasSTUserInfoFields = fieldValues.stream().anyMatch(fieldValue -> eUserSystemFieldDef.IsSTUserInfoSystemField(fieldValue.getFieldId()));
                if (hasSTUserInfoFields) {
                    var stUserInfoBo = new STUserInfoBo();
                    if( !isNew)
                        stUserInfoBo.setUserId(bo.getExternalUserId());
                    bo.setStUserInfo( stUserInfoBo );
                }

                for (var fieldValue : fieldValues) {
                    setFieldValue(bo, fieldValue);
                }
            }
            return MapstructUtils.convert(bo, UserInfo.class);
        } catch (ServiceException e) {
            throw new ServiceException("Failed to convert UserInfoBo to UserInfo: " + e.getMessage());
        }
    }
    @Override
    protected int getProjectId(UserInfoBo bo) {
        return bo.getProjectId();
    }

    @Override
    protected int getId(UserInfoBo bo) {
        return bo.getExternalUserId();
    }
    @Override
    protected boolean getAllModuleSystemFieldOriginalValue(Integer fieldId, IFieldValue oldFieldValue) {
        if (oldFieldValue == null || this.originalUserInfoVo == null)
            return true;

        switch (eUserSystemFieldDef.from(fieldId)) {
            case UserName:
                oldFieldValue.readValueFromDB(this.originalUserInfoVo.getUserName());
                break;
            case NickName:
                oldFieldValue.readValueFromDB(this.originalUserInfoVo.getNickName());
                break;
            case Email:
                oldFieldValue.readValueFromDB(this.originalUserInfoVo.getEmail());
                break;
            case Phone:
                oldFieldValue.readValueFromDB(this.originalUserInfoVo.getPhoneNumber());
                break;
            case Status:
                oldFieldValue.readValueFromDB(this.originalUserInfoVo.getStatus());
                break;
            case Depart:
                oldFieldValue.readValueFromDB(this.originalUserInfoVo.getDeptId());
                break;
            case CreatedTime:
                oldFieldValue.readValueFromDB(this.originalUserInfoVo.getCreateTime());
                break;
            case CreatedBy:
                oldFieldValue.readValueFromDB(this.originalUserInfoVo.getCreateBy());
                break;
            case JobTitle:
                oldFieldValue.readValueFromDB(this.originalUserInfoVo.getJobTitle());
                break;
            case JobDuty:
                oldFieldValue.readValueFromDB(this.originalUserInfoVo.getJobDuty());
                break;
            case SupportTeam:
                oldFieldValue.readValueFromDB(this.originalUserInfoVo.getSupportTeam());
                break;
            case PrimarySupport:
                oldFieldValue.readValueFromDB(this.originalUserInfoVo.getPrimarySupport());
                break;
            case SecondarySupport:
                oldFieldValue.readValueFromDB(this.originalUserInfoVo.getSecondarySupport());
                break;
            case IsVip:
                oldFieldValue.readValueFromDB(this.originalUserInfoVo.getIsVip());
                break;
            default:
                return getCustomFieldOriginalValue(this.originalUserInfoVo.getProjectId(),fieldId, oldFieldValue);
        }
        return true;
    }
    @Override
    protected boolean getIndividualSystemFieldOriginalValue(Integer fieldId,IFieldValue oldFieldValue){
        //to do
        return false;
    }
    /**
     * 从用户信息转换后的字段获取字段Vo值，包括系统字段和自定义字段
     */
    public void retrieveUserInfoFieldValues(){
        if( this.originalUserInfoVo == null)
            return ;
        //Get all module system fields
        if( this.originalUserInfoVo.getFields() == null)
            this.originalUserInfoVo.setFields(new ArrayList<>());

        Integer projectId = this.originalUserInfoVo.getProjectId();
        for (var enumfieldId : FieldIdHelper.AllUserInfoSystemFields){
            int fieldId = enumfieldId.getValue();

            FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId, eSTModuleIDDef.UserInfo, fieldId,0);
            IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
            if( oldFieldValue != null && getFieldOriginalValue(projectId,fieldId,oldFieldValue)){
                TFieldValueVo fieldValueVo = new TFieldValueVo();
                fieldValueVo.setFieldId(fieldId);
                if( fieldId == eUserSystemFieldDef.Depart.getValue()) {
                    fieldValueVo.setValue(this.originalUserInfoVo.getDeptName());
                }
                else {
                    fieldValueVo.setValue(oldFieldValue.getDisplayValue());
                }
                fieldValueVo.setChoiceId(oldFieldValue.getRawValue());
                originalUserInfoVo.getFields().add(fieldValueVo);
            }
        }

        //Get custom fields
        super.retrieveItemCustomFieldValues(projectId,0);

    }
    @Override
    protected void setAllModuleSystemFieldValue(UserInfoBo bo, Integer fieldId, IFieldValue newFieldValue) {
        switch (eUserSystemFieldDef.from(fieldId)) {
            case UserName:
                bo.setUserName(newFieldValue.toCustomFieldFormatString());
                break;
            case NickName:
                bo.setNickName(newFieldValue.toCustomFieldFormatString());
                break;
            case Email:
                bo.setEmail(newFieldValue.toCustomFieldFormatString());
                break;
            case Phone:
                bo.setPhoneNumber(newFieldValue.toCustomFieldFormatString());
                break;
            case Status:
                bo.setSex(ValueConvert.readString(newFieldValue.getRawValue()));
                break;
            case Depart:
                bo.setDeptId(ValueConvert.readLong(newFieldValue.getRawValue()));
                break;
            case JobTitle:
                bo.getStUserInfo().setJobTitle(ValueConvert.readInt(newFieldValue.getRawValue()));
                break;
            case JobDuty:
                bo.getStUserInfo().setJobDuty(ValueConvert.readInt(newFieldValue.getRawValue()));
                break;
            case SupportTeam:
                bo.getStUserInfo().setSupportTeam(ValueConvert.readInt(newFieldValue.getRawValue()));
                break;
            case PrimarySupport:
                bo.getStUserInfo().setPrimarySupport(ValueConvert.readInt(newFieldValue.getRawValue()));
                break;
            case SecondarySupport:
                bo.getStUserInfo().setSecondarySupport(ValueConvert.readInt(newFieldValue.getRawValue()));
                break;
            case IsVip:
                bo.getStUserInfo().setIsVip(ValueConvert.readInt(newFieldValue.getRawValue()));
                break;
            default:
                setCustomFieldValue(bo, fieldId, newFieldValue);
                break;
        }
    }

    @Override
    protected void setIndividualSystemFieldValue(UserInfoBo bo, Integer fieldId, IFieldValue newFieldValue) {

    }


}
