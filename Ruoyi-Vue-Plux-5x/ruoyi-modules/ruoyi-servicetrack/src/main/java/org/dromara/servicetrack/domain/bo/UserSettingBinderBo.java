package org.dromara.servicetrack.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.dromara.servicetrack.domain.UserSetting;

@Data
@EqualsAndHashCode(callSuper = true)
public class UserSettingBinderBo extends ProjectSettingBinderBo{

    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    public UserSetting convertToUserSetting(SettingInfoBo bo, Long id) {
        UserSetting userSetting = new UserSetting();
        userSetting.setProjectId(super.projectId);
        userSetting.setUserId(this.userId);
        if( id != null)
            userSetting.setId(id);

        userSetting.setSettingId(bo.getSettingId());
        userSetting.setSettingOption(bo.getSettingOption());
        userSetting.setSettingName(bo.getSettingName());
        userSetting.setSettingContent(bo.getSettingContent());

        return userSetting;
    }
}
