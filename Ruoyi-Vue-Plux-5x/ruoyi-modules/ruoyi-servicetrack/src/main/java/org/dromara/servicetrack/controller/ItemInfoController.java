package org.dromara.servicetrack.controller;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.vo.ListViewFieldVo;
import org.dromara.common.servicetrack.domain.vo.WorkflowTransitionStateVo;
import org.dromara.common.web.core.BaseController;
import org.dromara.servicetrack.domain.bo.ItemInfoBo;
import org.dromara.servicetrack.domain.bo.ItemListBo;
import org.dromara.servicetrack.domain.bo.ItemTempInfoBo;
import org.dromara.servicetrack.domain.vo.ItemHistoryModelVo;
import org.dromara.servicetrack.domain.vo.ItemInfoVo;
import org.dromara.servicetrack.domain.vo.ItemListVo;
import org.dromara.servicetrack.domain.vo.ItemTempInfoListVo;
import org.dromara.servicetrack.service.IItemInfoService;
import org.dromara.servicetrack.service.IItemTempInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 条目管理
 *
 * <AUTHOR> fei
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/item")
public class ItemInfoController extends BaseController{
    private final IItemInfoService itemInfoService;
    private final IItemTempInfoService itemTempInfoService;
    /**
     * 查询条目列表
     */
    @GetMapping("/list")
    public TableDataInfo<ItemListVo> list(ItemListBo bo, PageQuery pageQuery) {
       return itemInfoService.selectPageItemList(bo, pageQuery);
    }
    /**
     * 获取条目详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<ItemInfoVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(itemInfoService.queryById(id));
    }
    /**
     * 根据Project Id和Item Id获得条目列表
     */
    @GetMapping("/getInfo")
    public R<ItemInfoVo> getItemInfo(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        @NotNull(message = "ItemId不能为空") @RequestParam Integer itemId) {
        return R.ok(itemInfoService.getItemDetail(projectId, itemId,projectId));
    }
    @GetMapping("/getHistory")
    public R<ItemHistoryModelVo> getItemHistory(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        @NotNull(message = "ItemId不能为空") @RequestParam Integer itemId) {
        return R.ok(itemInfoService.getItemHistory(projectId, itemId));
    }
    /**
     * 根据Project Id和Item Id获得条目列表
     */
    @GetMapping("/getPreAllocatedItemId")
    public R<Integer> getPreAllocatedItemId(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId) {
        return R.ok(itemInfoService.getPreAllocatedItemId(projectId));
    }
    /**
     * 查询条目列表
     */
    @GetMapping("/getTemplatelist")
    public R<List<ItemTempInfoListVo>> getTemplateList(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
         @RequestParam Integer groupId,@RequestParam Integer needItemType,@RequestParam Integer filterType){
        return R.ok(itemTempInfoService.getItemTempInfoList(projectId,groupId,needItemType,filterType));
    }
    /**
     * 根据Project Id和template Id获得条目列表
     */
    @GetMapping("/getTempInfo")
    public R<ItemInfoVo> getItemTempInfo(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        @NotNull(message = "TemplateId不能为空") @RequestParam Integer templateId) {
        return R.ok(itemInfoService.getItemTemplateDetail(projectId, templateId));
    }
    @GetMapping("/getNextState")
    public R<WorkflowTransitionStateVo> getItemNextState(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        @NotNull(message = "ItemId不能为空") @RequestParam Integer itemId,
        @NotNull(message = "StateId不能为空") @RequestParam Integer stateId) {
        return R.ok(itemInfoService.getItemNextState(projectId, itemId,stateId));
    }

    /**
     * 新增条目
     */
    @Log(title = "条目管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Integer> add(@Validated @RequestBody ItemInfoBo bo) {
        return R.ok(itemInfoService.insertByBo(bo));
    }

    /**
     * 修改条目
     */
    @Log(title = "条目管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Integer> edit(@Validated(EditGroup.class) @RequestBody ItemInfoBo bo) {
        return R.ok(itemInfoService.updateByBo(bo));
    }

    /**
     * 删除条目
     *
     * @param ids 主键串
     */
    @Log(title = "条目管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(itemInfoService.deleteWithValidByIds(List.of(ids), true));
    }
    /**
     * 新增条目模板
     */
    @Log(title = "条目模板管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/template")
    public R<Integer> addTemplate(@Validated @RequestBody ItemTempInfoBo bo) {
        return R.ok(itemInfoService.insertTemplateByBo(bo));
    }

    /**
     * 修改条目模板
     */
    @Log(title = "条目模板管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/template")
    public R<Integer> editTemplate(@Validated(EditGroup.class) @RequestBody ItemTempInfoBo bo) {
        return R.ok(itemInfoService.updateTemplateByBo(bo));
    }

    /**
     * 删除条目模板
     *
     * @param ids 主键串
     */
    @Log(title = "条目模板管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/template/{ids}")
    public R<Void> removeTemplate(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(itemInfoService.deleteTemplateWithValidByIds(List.of(ids), true));
    }

    @GetMapping("/getListviewFields")
    public R<ListViewFieldVo> getListviewFields(  @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                  @NotNull(message = "Option不能为空") @RequestParam Integer option){
        return R.ok(itemInfoService.getListviewFields(projectId,option));
    }
}
