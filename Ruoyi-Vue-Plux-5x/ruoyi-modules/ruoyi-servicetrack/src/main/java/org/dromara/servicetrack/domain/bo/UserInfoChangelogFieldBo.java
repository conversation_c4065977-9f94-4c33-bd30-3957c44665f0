package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.UserInfoChangelog;
import org.dromara.servicetrack.domain.UserInfoChangelogField;

/**
 * 用户日志对象 user_info_changelog_field
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserInfoChangelogField.class)
public class UserInfoChangelogFieldBo extends BaseChangelogFieldBo{
    /**
     * user id
     */
    private Integer userId;
}
