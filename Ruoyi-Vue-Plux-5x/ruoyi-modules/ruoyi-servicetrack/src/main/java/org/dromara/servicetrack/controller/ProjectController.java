package org.dromara.servicetrack.controller;

import io.swagger.v3.oas.models.security.SecurityScheme;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.servicetrack.constant.eProjectSetting;
import org.dromara.common.servicetrack.domain.bo.ProjectInfoBo;
import org.dromara.common.servicetrack.domain.bo.ProjectItemTypeBo;
import org.dromara.common.servicetrack.domain.vo.*;
import org.dromara.common.servicetrack.service.IProjectInfoService;
import org.dromara.common.servicetrack.service.IProjectItemTypeService;
import org.dromara.common.web.core.BaseController;
import org.dromara.servicetrack.domain.bo.ProjectSettingBinderBo;
import org.dromara.servicetrack.domain.bo.UserSettingBinderBo;
import org.dromara.servicetrack.domain.vo.ProjectSettingVo;
import org.dromara.servicetrack.domain.vo.UserSettingVo;
import org.dromara.servicetrack.logic.ProjectLogic;
import org.dromara.servicetrack.service.IProjectSettingService;
import org.dromara.servicetrack.service.IUserSettingService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 项目管理
 *
 * <AUTHOR> fei
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/project")
public class ProjectController extends BaseController {
    private final IProjectInfoService projectInfoService;
    private final IProjectItemTypeService projectItemTypeService;
    private final IProjectSettingService projectSettingService;
    private final IUserSettingService userSettingService;
    /**
     * 获取ID项目详情
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<ProjectInfoVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(projectInfoService.queryById(id));
    }
    /**
     * 根据Project Id获得项目详情
     */
    @GetMapping("/getInfo")
    public R<ProjectInfoVo> getItemInfo(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId) {
        return R.ok(projectInfoService.selectProjectInfoById(projectId));
    }
    /**
     * 查询项目列表
     */
    @GetMapping("/list")
    public List<ProjectInfoVo> list() {
        ProjectInfoBo bo = new ProjectInfoBo();
        return projectInfoService.queryList(bo);
    }
    @GetMapping("/listByUser")
    public R<List<ProjectInfoVo>> listByUser() {
        var projectInfos = projectInfoService.queryListByUser();
        if( projectInfos == null || projectInfos.isEmpty())
            return R.ok();
        List<Integer> workProjectIds = new ArrayList<>();
        for (ProjectInfoVo projectInfo : projectInfos) {
            if( projectInfo.getChildren() == null || projectInfo.getChildren().isEmpty())
                continue;;
            workProjectIds.addAll(projectInfo.getChildren().stream().map(ProjectInfoVo::getProjectId).toList());
        }
        if(!workProjectIds.isEmpty()){
            Integer settingId = eProjectSetting.Project_Alias.getValue();
            List<Integer> listSetting = new ArrayList<>();
            listSetting.add(settingId);
           var projectAlias = projectSettingService.selectSettingList(workProjectIds, listSetting);
           if(projectAlias != null && !projectAlias.isEmpty()){
               for (ProjectInfoVo projectInfo : projectInfos) {
                   if( projectInfo.getChildren() == null || projectInfo.getChildren().isEmpty())
                       continue;;
                   for (ProjectInfoVo projectInfoVo : projectInfo.getChildren()){
                       var aliasSetting = projectAlias.stream().filter(x -> x.getProjectId().equals(projectInfoVo.getProjectId())).findFirst().orElse( null);
                       String alias = projectInfoVo.getProjectName();
                       if( aliasSetting != null){
                           alias = aliasSetting.getSettingContent();
                       }
                       //substring 4 string
                       if( alias.length() > 4)
                         alias = alias.substring(0,4);
                       projectInfoVo.setAlias(alias);
                   }
               }
           }
        }

        return R.ok(projectInfos);
    }

    /**
     * 新增条目
     */
    @Log(title = "项目管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Integer> add(@Validated @RequestBody ProjectInfoBo bo) {
        return R.ok(projectInfoService.insertByBo(bo));
    }

    /**
     * 修改条目
     */
    @Log(title = "项目管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Integer> edit(@Validated @RequestBody ProjectInfoBo bo) {
        return R.ok(projectInfoService.updateByBo(bo));
    }

    /**
     * 删除条目
     *
     * @param ids 主键串
     */
    @Log(title = "项目管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectInfoService.deleteWithValidByIds(List.of(ids), true));
    }
    /**
     * 获取项目用户列表
     *
     * @param projectId 项目ID
     * @return 项目用户列表
     */
    @GetMapping("/getMemberList")
    public R<List<ProjectMemberVo>> getOwnerList(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId){
        ProjectLogic projectLogic = new ProjectLogic(projectId, Objects.requireNonNull(LoginHelper.getLoginUser()).getExternalUserId());
        return R.ok(projectLogic.getMemberList());
    }

    /**
     * 获取项目状态列表
     *
     * @param projectId 项目ID
     * @return 项目状态列表
     */
    @GetMapping("/getStateList")
    public R<List<WorkflowStateVo>> getStateList(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId){
        ProjectLogic projectLogic = new ProjectLogic(projectId, Objects.requireNonNull(LoginHelper.getLoginUser()).getExternalUserId());
        return R.ok(projectLogic.getStateList());
    }
    /**
     * 获取项目 transition列表
     *
     * @param projectId 项目ID
     * @return 项目 transition列表
     */
    @GetMapping("/getTransitionList")
    public R<List<WorkflowTransitionVo>> getTransitionList(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId){
        ProjectLogic projectLogic = new ProjectLogic(projectId, Objects.requireNonNull(LoginHelper.getLoginUser()).getExternalUserId());
        return R.ok(projectLogic.getTransitionList());
    }
    /**
     * 重置在缓存中的项目设置
     *
     * @param projectId 项目ID: 如果为空，则重置所有项目设置
     */
    @GetMapping("/ResetProjectSettings")
    public R<Void> ResetProjectSettings(
    @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId){
        ProjectLogic projectLogic = new ProjectLogic(projectId, Objects.requireNonNull(LoginHelper.getLoginUser()).getExternalUserId());
        projectLogic.resetProjectSetting();
        return R.ok();
    }

    //item type
    @GetMapping("/itemType/list")
    public R<List<ProjectItemTypeVo>> getItemTypeList(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId){
         return R.ok(projectItemTypeService.selectItemTypeList(projectId));
    }
    /**
     * 新增项目条目类型
     */
    @Log(title = "项目条目类型管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/itemType")
    public R<Integer> addItemType(@Validated @RequestBody ProjectItemTypeBo bo) {
        return R.ok(projectItemTypeService.insertByBo(bo));
    }

    /**
     * 修改项目条目类型
     */
    @Log(title = "项目条目类型管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/itemType")
    public R<Integer> editItemType(@Validated @RequestBody ProjectItemTypeBo bo) {
        return R.ok(projectItemTypeService.updateByBo(bo));
    }

    /**
     * 删除项目条目类型
     *
     * @param ids 主键串
     */
    @Log(title = "项目条目类型管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/itemType/{ids}")
    public R<Void> removeItemType(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectItemTypeService.deleteWithValidByIds(List.of(ids), true));
    }
    //end of item type

    // project Setting
    /**
     * 查询项目设置列表
     */
    @GetMapping("/setting/list")
    public R<List<ProjectSettingVo>> getSettinglist(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                 @NotNull(message = "SettingIds不能为空") @RequestParam List<Integer> settingIds) {

        return R.ok(projectSettingService.selectSettingList(projectId, settingIds));
    }
    /**
     *  更新项目设置类型
     */
    @Log(title = "项目设置管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/setting")
    public R<Void> editSetting(@Validated @RequestBody ProjectSettingBinderBo bo) {
        return toAjax(projectSettingService.updateByBo(bo));
    }

    /**
     * 删除项目设置
     *
     * @param ids 主键串
     */
    @Log(title = "项目设置管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/setting/{ids}")
    public R<Void> removeSetting(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectSettingService.deleteWithValidByIds(List.of(ids), true));
    }
    //end of project Setting

    //user setting
    /**
     * 查询项目用户设置列表
     */
    @GetMapping("/userSetting/list")
    public R<List<UserSettingVo>> getUserSettinglist(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                  @NotNull(message = "userId不能为空") @RequestParam Integer userId,
                                                  @NotNull(message = "SettingIds不能为空") @RequestParam List<Integer> settingIds) {

        return R.ok(userSettingService.selectUserSettingList(projectId,userId, settingIds));
    }
    /**
     *  更新项目用户设置类型
     */
    @Log(title = "项目用户设置管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/userSetting")
    public R<Void> editUserSetting(@Validated @RequestBody UserSettingBinderBo bo) {
        return toAjax(userSettingService.updateUserSetting(bo));
    }

    /**
     * 删除项目用户设置
     *
     * @param ids 主键串
     */
    @Log(title = "项目用户设置管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/userSetting/{ids}")
    public R<Void> removeUserSetting(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(userSettingService.deleteWithValidByIds(List.of(ids), true));
    }
    //end of user setting
}
