package org.dromara.servicetrack.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.UserInfo;
import org.dromara.servicetrack.model.field.TFieldValueVo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户信息视图对象 sys_user info
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserInfo.class)
public class STUserInfoVo extends BaseInfoVo implements Serializable {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 基础项目ID
     */
    @JsonIgnore
    private Integer projectId;

    /**
     * 用户账号
     */
    @JsonIgnore
    private String userName;

    /**
     * 用户昵称
     */
    @JsonIgnore
    private String nickName;

    /**
     * 部门ID
     */
    @JsonIgnore
    private Long deptId;

    @JsonIgnore
    private String deptName;
    /**
     * 用户邮箱
     */
    @JsonIgnore
    private String email;

    /**
     * 手机号码
     */
    @JsonIgnore
    private String phoneNumber;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date createTime;

    /**
     * 创建者
     */
    @JsonIgnore
    private Integer CreateBy;
    /**
     * 用户状态（0正常 1停用）
     */
    @JsonIgnore
    private Integer status;
    /**
     * 外部用户ID
     */
    private Integer externalUserId;

    /**
     * service track 用户类型(refer to eSTModuleType): 1:EP, 2:SP, 3: EP & SP
     */
    private Integer stUserType;

    /**
     * 是否为service track管理员
     */
    private Integer  stAdmin;

    /**
     * 职位
     */
    @JsonIgnore
    private Integer jobTitle;

    /**
     * 职务
     */
    @JsonIgnore
    private Integer jobDuty;
    /**
     * 团队
     */
    @JsonIgnore
    private Integer supportTeam;
    /**
     * 首要支持人
     */
    @JsonIgnore
    private Integer primarySupport;

    /**
     * 次要支持人
     */
    @JsonIgnore
    private Integer secondarySupport;

    /**
     * 是否VIP
     */
    @JsonIgnore
    private Integer isVip;

    /*
     * 头像
     */
    private  Long  avatar;
    /**
     * 头像URL
     */
    private  String  avatarUrl;
    /**
     * 字段 values
     */
    private List<TFieldValueVo> fields;

    /**
     * 变更记录
     */
    private List<UserInfoChangelogVo> changelogs;
}
