package org.dromara.servicetrack.controller;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectGroupBo;
import org.dromara.common.servicetrack.domain.bo.ProjectGroupUserBo;
import org.dromara.common.servicetrack.domain.bo.ProjectMemberBo;
import org.dromara.common.servicetrack.domain.vo.ProjectGroupUserVo;
import org.dromara.common.servicetrack.domain.vo.ProjectGroupVo;
import org.dromara.common.servicetrack.domain.vo.ProjectMemberVo;
import org.dromara.common.servicetrack.service.IProjectGroupService;
import org.dromara.common.servicetrack.service.IProjectGroupUserService;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目分组管理
 *
 * <AUTHOR> fei
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/projectGroup")
public class ProjectGroupController  extends BaseController {
    private final IProjectGroupService projectGroupService;
    private final IProjectGroupUserService projectGroupUserService;

    @GetMapping("/list")
    public R<List<ProjectGroupVo>> list(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId) {
        return R.ok(projectGroupService.selectGroupList(projectId));
    }
    /**
     * 新增项目分组
     */
    @Log(title = "项目分组管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Integer> add(@Validated @RequestBody ProjectGroupBo bo) {
        return R.ok(projectGroupService.insertByBo(bo));
    }

    /**
     * 修改项目分组
     */
    @Log(title = "项目分组管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Integer> edit(@Validated @RequestBody ProjectGroupBo bo) {
        return R.ok(projectGroupService.updateByBo(bo));
    }

    /**
     * 删除项目分组
     *
     * @param ids 主键串
     */
    @Log(title = "项目分组管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectGroupService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 查询项目分组用户列表
     */
    @GetMapping("/user/list")
    public R<List<ProjectGroupUserVo>> list(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        @NotNull(message = "GroupId不能为空") @RequestParam Integer groupId) {
        return R.ok(projectGroupUserService.selectGroupUserList(projectId,groupId));
    }
    /**
     * 新增项目分组用户
     */
    @Log(title = "项目分组用户管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/user")
    public R<Boolean> addGroupUser(@Validated @RequestBody ProjectGroupUserBo bo) {
        return R.ok(projectGroupUserService.insertByBo(bo));
    }

    /**
     * 修改项目分组用户
     */
    @Log(title = "项目分组用户管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/user")
    public R<Boolean> editGroupUser(@Validated @RequestBody ProjectGroupUserBo bo) {
        return R.ok(projectGroupUserService.updateByBo(bo));
    }

    /**
     * 删除项目分组用户
     *
     * @param ids 主键串
     */
    @Log(title = "项目分组用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/user/{ids}")
    public R<Void> removeGroupUser(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectGroupUserService.deleteWithValidByIds(List.of(ids), true));
    }
}
