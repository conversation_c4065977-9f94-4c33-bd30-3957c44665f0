package org.dromara.servicetrack.domain.vo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.servicetrack.domain.vo.WorkflowTransitionStateVo;
import org.dromara.servicetrack.domain.ItemInfo;
import org.dromara.servicetrack.model.field.TFieldValueVo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 条目视图对象 item_info
 *
 * <AUTHOR> fei
 */

@Data
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemInfo.class)
public class ItemInfoVo extends BaseInfoVo implements Serializable {

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * project id
     */
    @ExcelProperty(value = "项目ID")
    private Integer projectId;

    /**
     * item id
     */
    @ExcelProperty(value = "条目ID")
    private Integer itemId;


    /**
     * display id
     */
    @ExcelProperty(value = "显示ID")
    private String displayId;

    /**
     * item title
     */
    @ExcelProperty(value = "条目标题")
    private String itemTitle;

    /**
     * module id
     */
    @ExcelProperty(value = "模块ID")
    private Integer moduleId;

    /**
     * type id
     */
    @ExcelProperty(value = "类型ID")
    private Integer typeId;

    /**
     * owner id
     */
    @ExcelProperty(value = "条目拥有者ID")
    private Integer ownerId;

    /**
     * Owner name
     */
    @ExcelProperty(value = "条目拥有者名称")
   private String ownerName;

    /**
     * state id
     */
    @ExcelProperty(value = "条目状态ID")
    private Integer stateId;

    /**
     * status name
     */
    @ExcelProperty(value = "条目状态名称")
    private String stateName;

    /**
     * created time
     */
    @ExcelProperty(value = "创建时间")
    private Date createdTime;

    /**
     * created by
     */
    private int createdBy;

    /**
     * created by name
     */
    @ExcelProperty(value = "创建者名称")
    private String createdByName;

    /**
     * modified time
     */
    @ExcelProperty(value = "修改时间")
    private Date modifiedTime;

    /**
     * modified by
     */
     private int modifiedBy;

     /**
      * modified by name
     */
    @ExcelProperty(value = "修改者名称")
    private String modifiedByName;
    /**
     * assigned time
     */
    @ExcelProperty(value = "分配时间")
    private Date assignedTime;

    /**
     * contact id
     */
    private Integer contactId;

    /**
     * contact name
     */
    private Integer contactName;
    /**
     * 下一个工作流转变状态
     */
    private WorkflowTransitionStateVo nextWorkflowTransitionState;

    /**
     * 字段 values
     */
    private List<TFieldValueVo> fields;
}
