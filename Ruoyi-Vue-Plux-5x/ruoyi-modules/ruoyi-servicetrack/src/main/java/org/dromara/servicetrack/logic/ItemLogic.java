package org.dromara.servicetrack.logic;


import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.UserConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.servicetrack.constant.eSTModuleIDDef;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.constant.eFieldTypeDef;
import org.dromara.common.servicetrack.constant.eSystemFieldDef;
import org.dromara.common.servicetrack.domain.vo.WorkflowTransitionStateVo;
import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.logic.fieldvalue.*;
import org.dromara.common.servicetrack.logic.helper.FieldIdHelper;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.utils.ValueConvert;
import org.dromara.servicetrack.domain.ItemInfo;
import org.dromara.servicetrack.domain.bo.*;
import org.dromara.servicetrack.domain.vo.ItemInfoVo;
import org.dromara.common.servicetrack.domain.bo.FieldValueBo;
import org.dromara.servicetrack.domain.vo.STUserInfoVo;
import org.dromara.servicetrack.model.field.TFieldValueVo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 条目业务对象 item logic
 *
 * <AUTHOR> fei
 */
@Slf4j
public class ItemLogic extends AbstractEntityLogic<ItemInfoBo, ItemInfoVo>  {
    private final TableSequenceManager tableSequenceManager;
    private final ItemInfoVo originalItemInfoVo;
    public ItemLogic(TableSequenceManager tableSequenceManager, Integer UserId, ItemInfoVo originalItemInfVo) {
        super(tableSequenceManager, UserId,originalItemInfVo,InfoVoType.ITEM_INFO);
        this.tableSequenceManager = tableSequenceManager;
        this.originalItemInfoVo = originalItemInfVo;
    }

    /**
     * 将ItemInfoBo转换为ItemInfo
     *
     * @param bo ItemInfoBo对象
     * @return 转换后的ItemInfo对象
     */
    public ItemInfo ConvertToItemInfo(ItemInfoBo bo) {
        try {
            if (bo == null) {
                throw new ServiceException("ItemInfoBo cannot be null");
            }
            if (bo.getProjectId() == null) {
                throw new ServiceException("ProjectId cannot be null");
            }
            boolean isNew =  bo.getItemId() == null || bo.getItemId() == 0;
            int itemId = isNew ? ( (bo.getPreAllocatedItemId() != null && bo.getPreAllocatedItemId() > 0) ? bo.getPreAllocatedItemId() : tableSequenceManager.getNextSequence(SequenceTable.Item_Info, bo.getProjectId()))
                            :bo.getItemId();
            bo.setItemId(itemId);
            bo.setDisplayId(UserConstants.ITEM_DISPLAY_ID_PREFIX + itemId);
            Date currentTime = DateUtils.getNowDate();
            bo.setModifiedTime(currentTime);
            bo.setAssignedTime(currentTime);
            if( isNew ) {
                bo.setCreatedBy(UserId);
                bo.setCreatedTime(currentTime);
            }
            bo.setModifiedBy(UserId);

            var fieldValues = bo.getFields();
            if( fieldValues != null && !fieldValues.isEmpty())
            {
                //remove duplicate fields while keeping the last occurrence of each fieldId
                Map<Integer, FieldValueBo> uniqueFields = new java.util.LinkedHashMap<>();
                for (var field : fieldValues) {
                    uniqueFields.put(field.getFieldId(), field);
                }
                fieldValues.clear();
                fieldValues.addAll(uniqueFields.values());


                for (var fieldValue : fieldValues){
                    setFieldValue(bo,fieldValue);
                }
            }
            return MapstructUtils.convert(bo, ItemInfo.class);
        } catch (ServiceException e) {
            throw new ServiceException("Failed to convert ItemInfoBo to ItemInfo:" + e.getMessage());
        }
    }

    @Override
    protected int getProjectId(ItemInfoBo bo) {
        return bo.getProjectId();
    }

    @Override
    protected int getId(ItemInfoBo bo) {
        return bo.getItemId();
    }

    @Override
    protected void setAllModuleSystemFieldValue(ItemInfoBo bo,Integer fieldId,IFieldValue newFieldValue){
        if( fieldId == eSystemFieldDef.Title.getValue()){
            bo.setItemTitle(newFieldValue.toCustomFieldFormatString());
        }
        else  if( fieldId == eSystemFieldDef.Status.getValue() ){
            bo.setStateId(ValueConvert.readInt(newFieldValue.getRawValue()));
        }
        else if( fieldId == eSystemFieldDef.Owner.getValue()){
            bo.setOwnerId(ValueConvert.readInt(newFieldValue.getRawValue()));
        }
        else if ( fieldId == eSystemFieldDef.Type.getValue()){
            bo.setTypeId(ValueConvert.readInt(newFieldValue.getRawValue()));
        }
        else if( fieldId == eSystemFieldDef.Description.getValue()){
            setCustomFieldValue(bo,fieldId,newFieldValue);
        }

    }
    @Override
    protected void setIndividualSystemFieldValue(ItemInfoBo bo,Integer fieldId,IFieldValue newFieldValue){
        //to do
    }

    @Override
    protected boolean  getAllModuleSystemFieldOriginalValue(Integer fieldId,IFieldValue oldFieldValue){
        if( oldFieldValue == null || this.originalItemInfoVo == null)
            return true;
        if( fieldId == eSystemFieldDef.Title.getValue()){
            oldFieldValue.readValueFromDB(this.originalItemInfoVo.getItemTitle());
        }
        else  if( fieldId == eSystemFieldDef.Owner.getValue() ){
            oldFieldValue.readValueFromDB( this.originalItemInfoVo.getOwnerId());
        }
        else if( fieldId == eSystemFieldDef.Status.getValue()){
            oldFieldValue.readValueFromDB(this.originalItemInfoVo.getStateId());
        }
        else if ( fieldId == eSystemFieldDef.Type.getValue()){
            oldFieldValue.readValueFromDB( this.originalItemInfoVo.getTypeId());
        }
        else if( fieldId == eSystemFieldDef.Description.getValue()){
            return getCustomFieldOriginalValue(this.originalItemInfoVo.getProjectId(),fieldId,oldFieldValue);
        }
        return true;
    }
    @Override
    protected boolean getIndividualSystemFieldOriginalValue(Integer fieldId,IFieldValue oldFieldValue){
        //to do
        return false;
    }

    /**
     * 从条目转换后的字段获取字段Vo值，包括系统字段和自定义字段
     */
    public void retrieveItemFieldValues(Integer workProjectId){
        if( this.originalItemInfoVo == null)
            return ;
       //Get all module system fields
        if( this.originalItemInfoVo.getFields() == null)
            this.originalItemInfoVo.setFields(new ArrayList<>());

        Integer projectId = this.originalItemInfoVo.getProjectId();
        for (var enumfieldId : FieldIdHelper.AllModuleSystemFields){
            int fieldId = enumfieldId.getValue();
            if( fieldId == eSystemFieldDef.Description.getValue())//using getTextFields to handle description field
                continue;
            if( fieldId == eSystemFieldDef.Attachment.getValue()){

                var attachments = this.originalItemInfoVo.getAttachments();
                if( attachments != null && !attachments.isEmpty()){

                    TFieldValueVo attachmentFieldValue = new TFieldValueVo();
                    attachmentFieldValue.setFieldId(fieldId);
                    attachmentFieldValue.setValue(this.originalItemInfoVo.getAttachmentJsonValue());
                  originalItemInfoVo.getFields().add(attachmentFieldValue);
                }
                continue;
            }
            FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId, eSTModuleIDDef.Incident, fieldId,workProjectId);
            IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
            if( oldFieldValue != null && getFieldOriginalValue(projectId,fieldId,oldFieldValue)){
                TFieldValueVo fieldValueVo = new TFieldValueVo();
                fieldValueVo.setFieldId(fieldId);
                fieldValueVo.setValue(oldFieldValue.getDisplayValue());
                fieldValueVo.setChoiceId(oldFieldValue.getRawValue());
                originalItemInfoVo.getFields().add(fieldValueVo);
            }
        }
        //Get individual system fields
//        for (var fieldId : FieldIdHelper.IndividualSystemFields){
//
//        }
        //Get custom fields
        super.retrieveItemCustomFieldValues(projectId,workProjectId);

        //get workflow transition state
        if(this.originalItemInfoVo.getStateId() == null)//set default state id
            this.originalItemInfoVo.setStateId(0);

        if( this.originalItemInfoVo.getStateId() != null){
            WorkflowTransitionStateVo nextWorkflowTransitionState = ProjectManager.getInstance(workProjectId).getWorkflowTransitionNextState(this.originalItemInfoVo.getStateId());
            if( nextWorkflowTransitionState != null){
                this.originalItemInfoVo.setNextWorkflowTransitionState(nextWorkflowTransitionState);
            }
        }
    }
}
