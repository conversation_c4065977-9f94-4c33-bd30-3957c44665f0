package org.dromara.servicetrack.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户信息ListView视图对象 sys_user info
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
public class UserInfoListVo extends CustomFieldsVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户账号
     */
    @JsonIgnore
    private String userName;

    /**
     * 用户昵称
     */
    @JsonIgnore
    private String nickName;

    /**
     * 部门ID
     */
    @JsonIgnore
    private Long deptId;

    @JsonIgnore
    private String deptName;
    /**
     * 用户邮箱
     */
    @JsonIgnore
    private String email;

    /**
     * 手机号码
     */
    @JsonIgnore
    private String phoneNumber;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date createTime;

    /**
     * 创建者
     */
    @JsonIgnore
    private Integer createBy;

    /**
     * 创建者名称
     */
    @JsonIgnore
    private String createByName;
    /**
     * 用户状态（0正常 1停用）
     */
    @JsonIgnore
    private Integer status;
    /**
     * 外部用户ID
     */
    @JsonIgnore
    private Integer externalUserId;

    /**
     * service track 用户类型(refer to eSTModuleType): 1:EP, 2:SP, 3: EP & SP
     */
    private Integer stUserType;

    /**
     * 是否为service track管理员
     */
    private Integer  stAdmin;

    /**
     * User字段数据
     */
    private List<ListFieldVo> values;
}
