package org.dromara.servicetrack.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class BaseFieldVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;
    /**
     * project id
     */
    private Integer projectId;

    /**
     * field id
     */
    private Integer fieldId;

    /*
     * field type
     */
    private Integer fieldType;

    /*
     * field name
     */
    private String fieldName;
}
