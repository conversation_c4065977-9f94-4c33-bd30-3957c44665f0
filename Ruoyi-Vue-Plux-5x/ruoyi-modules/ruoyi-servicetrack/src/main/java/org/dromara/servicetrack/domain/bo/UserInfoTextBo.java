package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.UserInfoText;

/**
 * User Info Text 业务对象 user_info_text
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserInfoText.class)
public class UserInfoTextBo extends UserInfoFieldBo{
    /**
     * text
     */
    private String text;
}
