package org.dromara.servicetrack.logic;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.servicetrack.constant.eFieldTypeDef;
import org.dromara.common.servicetrack.constant.eSTModuleIDDef;
import org.dromara.common.servicetrack.constant.eSystemFieldDef;
import org.dromara.common.servicetrack.domain.bo.FieldValueBo;
import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.logic.fieldvalue.*;
import org.dromara.common.servicetrack.logic.helper.FieldIdHelper;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.utils.ValueConvert;
import org.dromara.servicetrack.domain.bo.*;
import org.dromara.servicetrack.domain.vo.BaseInfoVo;
import org.dromara.servicetrack.domain.vo.CustomFieldsVo;
import org.dromara.servicetrack.domain.vo.ItemInfoVo;
import org.dromara.servicetrack.domain.vo.STUserInfoVo;
import org.dromara.servicetrack.model.field.TFieldValueVo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 实体业务逻辑抽象基类
 * Abstract base class for entity logic operations
 *
 * <AUTHOR> fei
 */
@Slf4j
public abstract class AbstractEntityLogic<T extends BaseInfoBo, V extends BaseInfoVo> extends BaseLogic {

    public enum InfoVoType {
        ITEM_INFO,
        USER_INFO
    }
    protected final TableSequenceManager tableSequenceManager;
    protected final V originalEntityVo;
    protected final InfoVoType infoVoType;
    private BaseChangelogBo currentChangelogBo;
    public AbstractEntityLogic(TableSequenceManager tableSequenceManager, Integer UserId, V originalEntityVo, InfoVoType infoVoType) {
        super(UserId);
        this.tableSequenceManager = tableSequenceManager;
        this.originalEntityVo = originalEntityVo;
        this.infoVoType =  infoVoType;
        this.currentChangelogBo = null;
    }


    /**
     * 设置字段值
     * Set field value for the entity
     *
     * @param bo 业务对象
     * @param fieldValueBo 字段值业务对象
     */
    public void setFieldValue(T bo, FieldValueBo fieldValueBo) {
        int projectId = getProjectId(bo);
        int workProejectId = 0;
        int fieldId = fieldValueBo.getFieldId();
        if( fieldId <= 0)
            return;
        eSTModuleIDDef moduleId = this.infoVoType ==  InfoVoType.ITEM_INFO ? eSTModuleIDDef.Incident: eSTModuleIDDef.UserInfo;
        if( moduleId == eSTModuleIDDef.Incident && fieldId == eSystemFieldDef.Attachment.getValue())
            return;
        if( moduleId == eSTModuleIDDef.Incident){
            workProejectId = ((ItemInfoBo) bo).getWorkProjectId() != null ? ((ItemInfoBo) bo).getWorkProjectId() : 0;
        }

        FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId,moduleId, fieldId,workProejectId);
        IFieldValue newFieldValue = fieldValueHandler.createFieldValue();
        if (newFieldValue == null) {
            return;
        }

        newFieldValue.setFieldValue(fieldValueBo.getValue(), 0);
        IFieldValue oldFieldValue = this.originalEntityVo == null ? null :fieldValueHandler.createFieldValue();
        log.error("fieldId:{} in setFieldValue", fieldId);
        if (getFieldOriginalValue(getProjectId(bo),fieldId, oldFieldValue)) {
            if (newFieldValue.equalsTo(oldFieldValue)) {
                return;
            }
        }

        if (FieldIdHelper.IsAllModuleSystemField(fieldId)) {
            setAllModuleSystemFieldValue(bo, fieldId, newFieldValue);
        }
        else if (FieldIdHelper.IsIndividualSystemField(fieldId)) {
            setIndividualSystemFieldValue(bo, fieldId, newFieldValue);
        }
        else if (FieldIdHelper.IsCustomField(fieldId)) {
            setCustomFieldValue(bo, fieldId, newFieldValue);
        }

        // Generate changelog
        generateChangelog(bo, fieldId, newFieldValue, oldFieldValue);
    }

    /**
     * 设置所有模块通用系统字段值
     * Set common system field value for all modules
     *
     * @param bo 业务对象
     * @param fieldId 字段ID
     * @param newFieldValue 新字段值
     */
    protected abstract void setAllModuleSystemFieldValue(T bo, Integer fieldId, IFieldValue newFieldValue);
    protected abstract void setIndividualSystemFieldValue(T bo, Integer fieldId, IFieldValue newFieldValue);

    protected  Integer getCurrentProjectId(T bo)
    {
        var curProjectId = getProjectId(bo);
        if(this.infoVoType == InfoVoType.ITEM_INFO){
            var workProjectId = ((ItemInfoBo) bo).getWorkProjectId();
            if( workProjectId != null && workProjectId > 0)
                curProjectId = workProjectId;
        }
        return curProjectId;
    }
    /**
     * 设置自定义字段值
     * Set custom field value
     *
     * @param bo 业务对象
     * @param fieldId 字段ID
     * @param newFieldValue 新字段值
     */
    protected void setCustomFieldValue(T bo, Integer fieldId, IFieldValue newFieldValue)
    {
        if (bo == null || newFieldValue == null) {
            return;
        }

        // 获取字段类型
        var curProjectId = getCurrentProjectId(bo);
        Integer fieldTypeId = ProjectManager.getInstance(curProjectId).getFieldType(fieldId);
        if (fieldTypeId == null) {
            return;
        }

        // 根据字段类型处理
        eFieldTypeDef fieldType = eFieldTypeDef.from(fieldTypeId);
        if (fieldType == null) {
            return;
        }

        // 根据 originalEntityVo 的具体类型决定使用哪个业务对象
        setCustomFieldValueCore(bo, fieldId, newFieldValue, fieldType, this.infoVoType);
    }
    private BaseFieldBo createTextBo(T bo,  Integer fieldId,  IFieldValue newFieldValue,InfoVoType infoVoType) {
        BaseFieldBo newText = null;
        switch(infoVoType) {
            case ITEM_INFO:
            {
                newText = new ItemTextBo();
                ((ItemTextBo) newText).setItemId(getId(bo));
                ((ItemTextBo) newText).setText(newFieldValue.toCustomFieldFormatString());
                break;
            }
            case USER_INFO:
            {
                newText = new UserInfoTextBo();
                ((UserInfoTextBo) newText).setUserId(getId(bo));
                ((UserInfoTextBo) newText).setText(newFieldValue.toCustomFieldFormatString());
                break;
            }
            default:
                log.warn("Unsupported infoVoType: {}", infoVoType);
        }
        if (newText == null) {
            return null;
        }
       newText.setProjectId(getProjectId(bo));
       newText.setFieldId(fieldId);

        return newText;
    }
    private BaseFieldBo createSelectionBo(T bo,  Integer fieldId,Integer choiceId, IFieldValue newFieldValue,InfoVoType infoVoType) {
        BaseFieldBo newSelection = null;
        switch(infoVoType) {
            case ITEM_INFO:
            {
                newSelection = new ItemSelectionBo();
                ((ItemSelectionBo) newSelection).setItemId(getId(bo));
                ((ItemSelectionBo) newSelection).setChoiceId(choiceId);
                break;
            }
            case USER_INFO:
            {
                newSelection = new UserInfoSelectionBo();
                ((UserInfoSelectionBo) newSelection).setUserId(getId(bo));
                ((UserInfoSelectionBo) newSelection).setChoiceId(choiceId);
                break;
            }
            default:
                log.warn("Unsupported infoVoType: {}", infoVoType);
        }
        if (newSelection == null) {
            return null;
        }
        newSelection.setProjectId(getProjectId(bo));
        newSelection.setFieldId(fieldId);
        return newSelection;
    }
    private BaseFieldBo createDateTimeBo(T bo,  Integer fieldId, IFieldValue newFieldValue,InfoVoType infoVoType) {
        BaseFieldBo newDateTime = null;
        switch(infoVoType) {
            case ITEM_INFO:
            {
                newDateTime = new ItemDateTimeBo();
                ((ItemDateTimeBo) newDateTime).setItemId(getId(bo));
                ((ItemDateTimeBo) newDateTime).setDateTime(ValueConvert.readDateTime(newFieldValue.getRawValue()));
                break;
            }
            case USER_INFO:
            {
                newDateTime = new UserInfoDateTimeBo();
                ((UserInfoDateTimeBo) newDateTime).setUserId(getId(bo));
                ((UserInfoDateTimeBo) newDateTime).setDateTime(ValueConvert.readDateTime(newFieldValue.getRawValue()));
                break;
            }
            default:
                log.warn("Unsupported infoVoType: {}", infoVoType);
        }
        if (newDateTime == null) {
            return null;
        }
        newDateTime.setProjectId(getProjectId(bo));
        newDateTime.setFieldId(fieldId);
        return newDateTime;
    }
    private void setCustomFieldValueCore(T bo, Integer fieldId, IFieldValue newFieldValue, eFieldTypeDef fieldType, InfoVoType infoVoType) {
        // 处理文本字段（短文本、纯文本、富文本）
        if (fieldType == eFieldTypeDef.ShortText || fieldType == eFieldTypeDef.PlainText || fieldType == eFieldTypeDef.RichText) {
            if (bo.getTexts() == null) {
                bo.setTexts(new ArrayList<>());
            }
            var textList = bo.getTexts();
            var newText = createTextBo(bo, fieldId,  newFieldValue, infoVoType);
            if ( newText != null ) {
                textList.add(newText);
            }
        }
        // 处理选择字段（下拉框、多选框、单选框、复选框）
        else if (fieldType == eFieldTypeDef.Dropdown || fieldType == eFieldTypeDef.MultipleSelection ||
            fieldType == eFieldTypeDef.CheckBox || fieldType == eFieldTypeDef.RadioBox) {
            if (bo.getSelections() == null) {
                bo.setSelections(new ArrayList<>());
            }
            var selectionList = bo.getSelections();
            if (fieldType != eFieldTypeDef.MultipleSelection) {
                var newSelection = createSelectionBo(bo, fieldId, ValueConvert.readInt(newFieldValue.getRawValue()), newFieldValue, infoVoType);
                if ( newSelection != null ) {
                    selectionList.add(newSelection);
                }
            } else {
                var choiceIds = ValueConvert.readListInt(newFieldValue.getRawValue());
                for (Integer choiceId : choiceIds) {
                    var newSelection = createSelectionBo(bo, fieldId, choiceId, newFieldValue, infoVoType);
                    if( newSelection != null) {
                        selectionList.add(newSelection);
                    }
                }
            }
        }
        // 处理日期字段
        else if (fieldType == eFieldTypeDef.Date) {
            if (bo.getDateTimes() == null) {
                bo.setDateTimes(new ArrayList<>());
            }
            var dateTimeList = bo.getDateTimes();
            var newDateTime = createDateTimeBo(bo, fieldId, newFieldValue, infoVoType);
            if( newDateTime != null){
                dateTimeList.add(newDateTime);
            }
        }
        // 其他字段类型的处理逻辑
        else {
            log.warn("Unsupported field type: {}", fieldType);
        }
    }
    private BaseChangelogBo createChangelogBo(T bo) {
        if( currentChangelogBo != null)
            return currentChangelogBo;
        switch(this.infoVoType) {
            case ITEM_INFO:
            {
                currentChangelogBo = new ItemChangelogBo();
                Integer changelogId = tableSequenceManager.getNextSequence(SequenceTable.Item_Changelog,getProjectId(bo));
                currentChangelogBo.setChangelogId(changelogId);
                ((ItemChangelogBo) currentChangelogBo).setItemId(getId(bo));
                currentChangelogBo.setChangedById(UserId);
                break;
            }
            case USER_INFO:
            {
                currentChangelogBo = new UserInfoChangelogBo();
                Integer changelogId = tableSequenceManager.getNextSequence(SequenceTable.UserInfo_Changelog,getProjectId(bo));
                currentChangelogBo.setChangelogId(changelogId);
                currentChangelogBo.setChangedById( UserId);
                ((UserInfoChangelogBo) currentChangelogBo).setUserId(getId(bo));
                break;
            }
            default:
                log.warn("Unsupported infoVoType: {}", infoVoType);
        }
        if (currentChangelogBo == null) {
            return null;
        }
        currentChangelogBo.setProjectId(getProjectId(bo));
        currentChangelogBo.setLogTime(DateUtils.getNowDate());
        currentChangelogBo.setChangelogFields(new ArrayList<>());
        return currentChangelogBo;
    }
    private BaseChangelogFieldBo createChangelogFieldBo(T bo, Integer  changelogId, Integer fieldId, IFieldValue newFieldValue, IFieldValue oldFieldValue) {
        BaseChangelogFieldBo changelogField = null;
        switch(this.infoVoType) {
            case ITEM_INFO:
            {
                changelogField = new ItemChangelogFieldBo();
                changelogField.setChangelogId(changelogId);
                ((ItemChangelogFieldBo) changelogField).setItemId(getId(bo));

                break;
            }
            case USER_INFO:
            {
                changelogField = new UserInfoChangelogFieldBo();

                changelogField.setChangelogId(changelogId);
                ((UserInfoChangelogFieldBo) changelogField).setUserId(getId(bo));

                break;
            }
            default:
                log.warn("Unsupported infoVoType: {}", infoVoType);
        }
        if (changelogField == null) {
            return null;
        }
        changelogField.setProjectId(getProjectId(bo));
        changelogField.setFieldId(fieldId);

        return changelogField;
    }
    private BaseChangeTextBo createChangeTextBo(T bo, Integer fieldId, IFieldValue newFieldValue, IFieldValue oldFieldValue, Integer changelogId,String fromText,String toText) {
        BaseChangeTextBo changeText = null;
        switch(this.infoVoType) {
            case ITEM_INFO:
            {
                changeText = new ItemChangeTextBo();
                ((ItemChangeTextBo)changeText).setItemId(getId(bo));

                break;
            }
            case USER_INFO:
            {
                changeText = new UserInfoChangeTextBo();
                ((UserInfoChangeTextBo)changeText).setUserId(getId(bo));
                break;
            }
            default:
                log.warn("Unsupported infoVoType: {}", infoVoType);
        }
        if (changeText == null) {
            return null;
        }
        changeText.setProjectId(getProjectId(bo));
        changeText.setFieldId(fieldId);
        changeText.setChangelogId(changelogId);
        changeText.setChangeFrom(fromText);
        changeText.setChangeTo(toText);
        return changeText;
    }
    /**
     * 生成变更日志
     * Generate changelog
     *
     * @param bo 业务对象
     * @param fieldId 字段ID
     * @param newFieldValue 新字段值
     * @param oldFieldValue 旧字段值
     */
    protected void generateChangelog(T bo, Integer fieldId, IFieldValue newFieldValue, IFieldValue oldFieldValue){

        String fieldName = ProjectManager.getInstance(getProjectId(bo)).getFieldName(fieldId);
        var changelog = createChangelogBo(bo);
        if( changelog == null
            || changelog.getChangelogId() == null
            || changelog.getChangelogId() == 0){
            return;
        }
        else{
             if( bo.getChangelog() == null)
                 bo.setChangelog(changelog);
        }
        var changelogField = createChangelogFieldBo(bo,changelog.getChangelogId(),fieldId,newFieldValue,oldFieldValue);
        if( changelogField == null           ){
            return;
        }
        changelog.getChangelogFields().add(changelogField);
        if( newFieldValue instanceof RichTextFieldValue){
            String newField = newFieldValue.getDisplayValue();
            String oldField = oldFieldValue == null ? "" : oldFieldValue.getDisplayValue();
            if( newField.equals(oldField)){
                return ;
            }

            String oldFieldPlanText = StringUtils.htmlToText(oldField);
            if( oldFieldPlanText.length() > 500)
                oldFieldPlanText = oldFieldPlanText.substring(0,500);
            String newFieldPlainText = StringUtils.htmlToText(newField);
            if( newFieldPlainText.length() > 500)
                newFieldPlainText = newFieldPlainText.substring(0,500);
            changelogField.setChangeFrom(oldFieldPlanText);
            changelogField.setChangeTo(newFieldPlainText);

            //add change text
            if( bo.getChangeTexts() == null)
                bo.setChangeTexts(new ArrayList<>());

            var changeText = createChangeTextBo(bo, fieldId, newFieldValue, oldFieldValue, changelog.getChangelogId(),oldField,newField);
            if( changeText != null)
               bo.getChangeTexts().add(changeText);
        }
        else if( newFieldValue instanceof PlainTextFieldValue){
            String newField = newFieldValue.getDisplayValue();
            String oldField = oldFieldValue == null ? "" : oldFieldValue.getDisplayValue();
            if( newField.equals(oldField)){
                return ;
            }

            //add change text
            if( bo.getChangeTexts() == null)
                bo.setChangeTexts(new ArrayList<>());

            var changeText =createChangeTextBo(bo, fieldId, newFieldValue, oldFieldValue, changelog.getChangelogId(),oldField,newField);
            if( changeText != null)
                bo.getChangeTexts().add(changeText);

            if( oldField != null && oldField.length() > 500)
                oldField = oldField.substring(0,500);
            changelogField.setChangeFrom(oldField);
            if( newField.length() > 500)
                newField = newField.substring(0,500);
            changelogField.setChangeTo(newField);

        }
        else if(newFieldValue instanceof TextBoxFieldValue){
            String newField = newFieldValue.toCustomFieldFormatString();
            String oldField = oldFieldValue == null ? "" : oldFieldValue.getDisplayValue();
            if( newField.equals(oldField)){
                return ;
            }
            changelogField.setChangeFrom(oldField);
            changelogField.setChangeTo(newField);

        }
        else if (newFieldValue instanceof DropdownFieldValue || newFieldValue instanceof MultiSelectionListFieldValue
            || newFieldValue instanceof CheckBoxFieldValue){
            String newFieldDisplayValue = newFieldValue.getDisplayValue();
            String oldFieldDisplayValue = oldFieldValue == null ? "" : oldFieldValue.getDisplayValue();
            Object newValue = newFieldValue.getRawValue();
            Object oldValue = oldFieldValue == null ? null : oldFieldValue.getRawValue();

            String oldField = "";
            String newField = "";
            if( newFieldValue instanceof MultiSelectionListFieldValue){
                newValue = ValueConvert.readListInt(newValue);
                var newValueList = ValueConvert.safeCastToList(newValue);
                if( !newValueList.isEmpty()){
                    newField =  StringUtils.join(newValueList, ",");
                }

                oldValue = ValueConvert.readListInt(oldValue);
                var oldValueList = ValueConvert.safeCastToList(oldValue);
                if( !oldValueList.isEmpty())
                    oldField =  StringUtils.join(oldValueList, ",");
            }
            else{
                newValue = ValueConvert.readInt(newValue);
                newField = String.format("%d",newValue);
                oldValue = ValueConvert.readInt(oldValue);
                oldField = String.format("%d",oldValue);
            }
            String oldStrFieldValue = oldFieldDisplayValue!=null ? oldFieldDisplayValue:"";
            String newStrFieldValue = newFieldDisplayValue != null ? newFieldDisplayValue:"";
            String description = String.format("changed '%s' from '%s' to '%s'", fieldName,oldStrFieldValue , newStrFieldValue);
            changelogField.setDescription(description);
            changelogField.setChangeFrom(oldStrFieldValue);
            changelogField.setChangeTo(newStrFieldValue);

            if(this.infoVoType == InfoVoType.ITEM_INFO && (fieldId == eSystemFieldDef.Owner.getValue() || fieldId == eSystemFieldDef.Status.getValue())){
                int oldChoiceId = oldFieldValue != null ? ValueConvert.readInt(oldFieldValue.getRawValue()) : 0;
                int newChoiceId = ValueConvert.readInt(newFieldValue.getRawValue());
                ItemInfoBo itemInfoBo = (ItemInfoBo)bo;
                if( itemInfoBo.getHistoryBo() == null) {
                    var history = new ItemHistoryBo();
                    history.setProjectId(itemInfoBo.getProjectId());
                    history.setItemId(itemInfoBo.getItemId());
                    history.setUserId(UserId);
                    history.setSeqNo(tableSequenceManager.getNextSequence(SequenceTable.Item_History,itemInfoBo.getProjectId()));
                    history.setDateTime(DateUtils.getNowDate());
                    itemInfoBo.setHistoryBo(history);
                }
                if( fieldId == eSystemFieldDef.Owner.getValue()){
                    var hasStateField = bo.getFields().stream().anyMatch(field -> field.getFieldId() == eSystemFieldDef.Status.getValue());
                    if (!hasStateField) {//if state field has no change, then set current state into history state fields
                        Integer stateId = this.originalEntityVo != null ?  ((ItemInfoVo)this.originalEntityVo).getStateId(): 0 ;
                        itemInfoBo.getHistoryBo().setStateFrom(stateId);
                        itemInfoBo.getHistoryBo().setStateTo(stateId);
                    }
                    itemInfoBo.getHistoryBo().setOwnerFrom(oldChoiceId);
                    itemInfoBo.getHistoryBo().setOwnerTo(newChoiceId);
                }
                else {
                    var hasOwnerField = bo.getFields().stream().anyMatch(field -> field.getFieldId() == eSystemFieldDef.Owner.getValue());
                     if (!hasOwnerField) {//if owner field has no change, then set current owner into history owner fields
                         Integer ownerId = this.originalEntityVo != null ?  ((ItemInfoVo)this.originalEntityVo).getOwnerId(): 0 ;
                        itemInfoBo.getHistoryBo().setOwnerFrom(ownerId);
                        itemInfoBo.getHistoryBo().setOwnerTo(ownerId);
                    }
                    itemInfoBo.getHistoryBo().setStateFrom(oldChoiceId);
                    itemInfoBo.getHistoryBo().setStateTo(newChoiceId);
                    var transitionField = bo.getFields().stream().filter( field -> field.getFieldId() == eSystemFieldDef.Transition.getValue()).findFirst().orElse(null);
                     if( transitionField != null) {
                        var transValue = transitionField.getValue();
                        if( transValue != null) {
                            var transId = ValueConvert.readInt(transValue);
                            itemInfoBo.getHistoryBo().setTransition(transId);
                        }
                    }
                }
            }
        }
        else if( newFieldValue instanceof DateFieldValue ){
            String newField = newFieldValue.getDisplayValue();
            String oldField = oldFieldValue == null ? "" : oldFieldValue.getDisplayValue();
            if( newField.equals(oldField)){
                return ;
            }
            changelogField.setChangeFrom(oldField);
            changelogField.setChangeTo(newField);
        }
        if( changelogField.getDescription() == null ) {
            String description = String.format("changed ‘%s’ from '%s' to '%s'", fieldName, changelogField.getChangeFrom() != null ? changelogField.getChangeFrom():"", changelogField.getChangeTo());
            changelogField.setDescription(description);
        }
    }
    /**
     * 获取字段原始值
     * Get original field value
     *
     * @param fieldId 字段ID
     * @param oldFieldValue 旧字段值对象
     * @return 是否成功获取
     */
    protected boolean getFieldOriginalValue(Integer projectId,Integer fieldId,IFieldValue oldFieldValue){
        if( this.originalEntityVo == null){
            return true;
        }
        if(FieldIdHelper.IsAllModuleSystemField(fieldId)){
            return getAllModuleSystemFieldOriginalValue(fieldId,oldFieldValue);
        }
        else if(FieldIdHelper.IsIndividualSystemField(fieldId)){
            return getIndividualSystemFieldOriginalValue(fieldId,oldFieldValue);
        }
        else if( FieldIdHelper.IsCustomField(fieldId)){
            return getCustomFieldOriginalValue(projectId,fieldId,oldFieldValue);
        }
        return false;
    }

    /**
     * 获得所有模块系统字段的原始值
     * @param fieldId 字段ID
     * @param oldFieldValue 旧字段值对象
     * @return 是否成功获取
     */
    protected abstract  boolean  getAllModuleSystemFieldOriginalValue(Integer fieldId,IFieldValue oldFieldValue);

    /**
     * 获得所有独立模块系统字段的原始值
     * @param fieldId 字段ID
     * @param oldFieldValue 旧字段值对象
     * @return 是否成功获取
     */
    protected abstract boolean getIndividualSystemFieldOriginalValue(Integer fieldId,IFieldValue oldFieldValue);

    protected boolean getCustomFieldOriginalValue(Integer projectId,Integer fieldId,IFieldValue oldFieldValue){
        if( oldFieldValue == null || this.originalEntityVo == null)
            return true;
        Integer fieldTypeId = ProjectManager.getInstance(projectId).getFieldType(fieldId);
        if (fieldTypeId == null) {
            return false;
        }
        eFieldTypeDef fieldType = eFieldTypeDef.from(fieldTypeId);
        if (fieldType == null) {
            return false;
        }
        boolean result = false;
        if( fieldType == eFieldTypeDef.ShortText || fieldType == eFieldTypeDef.PlainText || fieldType == eFieldTypeDef.RichText){
            Map<String, String> textMap = this.originalEntityVo.getTextFields();
            if( textMap != null && !textMap.isEmpty()){
                String text = textMap.get(String.valueOf(fieldId));
                if (text != null) {
                    oldFieldValue.readValueFromDB(text);
                }
                result = true;
            }
        }
        else if( fieldType == eFieldTypeDef.Dropdown || fieldType == eFieldTypeDef.MultipleSelection ||
            fieldType == eFieldTypeDef.CheckBox || fieldType == eFieldTypeDef.RadioBox){
            var selectionMap = this.originalEntityVo.getSelectionFields();
            if( selectionMap != null && !selectionMap.isEmpty()){
                List<Integer> selectionList = selectionMap.get(String.valueOf(fieldId));
                if (selectionList != null) {
                    oldFieldValue.readValueFromDB(selectionList);
                }
                result = true;
            }
        }
        else if( fieldType == eFieldTypeDef.Date){
            var dateTimeMap = this.originalEntityVo.getDatetimeFields();
            if( dateTimeMap != null && !dateTimeMap.isEmpty()){
                Date dateTime = dateTimeMap.get(String.valueOf(fieldId));
                if (dateTime != null) {
                    oldFieldValue.readValueFromDB(dateTime);
                }
                result = true;
            }
        }
        else {
            log.error("Unsupported field type: {} for field(id:{}) in getCustomFieldOriginalValue", fieldType.getValue(), fieldId);
        }
        return result;
    }
    protected void retrieveItemCustomFieldValues(Integer projectId,Integer workProjectId){
        if( this.originalEntityVo == null){
            return ;
        }
        eSTModuleIDDef moduleId = this.infoVoType ==  InfoVoType.ITEM_INFO ? eSTModuleIDDef.Incident: eSTModuleIDDef.UserInfo;
        Map<String, String> textMap = this.originalEntityVo.getTextFields();
        if( textMap != null && !textMap.isEmpty()){
            for( var entry : textMap.entrySet()){
                int fieldId = Integer.parseInt(entry.getKey());
                FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId,moduleId, fieldId,workProjectId);
                IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
                if( oldFieldValue == null)
                    continue;
                oldFieldValue.readValueFromDB(entry.getValue());
                TFieldValueVo fieldValueVo = new TFieldValueVo();
                fieldValueVo.setFieldId(fieldId);
                if( oldFieldValue instanceof RichTextFieldValue){
                    fieldValueVo.setValue(ValueConvert.encodeBase64(oldFieldValue.getDisplayValue()));
                }
                else{
                    fieldValueVo.setValue(oldFieldValue.getDisplayValue());
                }

                if(this.infoVoType == InfoVoType.ITEM_INFO){
                    ((ItemInfoVo)this.originalEntityVo).getFields().add(fieldValueVo);
                }
                else if(this.infoVoType == InfoVoType.USER_INFO){
                    ((STUserInfoVo)this.originalEntityVo).getFields().add(fieldValueVo);
                }

            }
        }

        var selectionMap = this.originalEntityVo.getSelectionFields();
        if( selectionMap != null && !selectionMap.isEmpty()){
            for( var entry : selectionMap.entrySet()){
                int fieldId = Integer.parseInt(entry.getKey());
                FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId,moduleId, fieldId,workProjectId);
                IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
                if( oldFieldValue == null)
                    continue;
                oldFieldValue.readValueFromDB(entry.getValue());
                TFieldValueVo fieldValueVo = new TFieldValueVo();
                fieldValueVo.setFieldId(fieldId);
                fieldValueVo.setValue(oldFieldValue.getDisplayValue());
                fieldValueVo.setChoiceId(oldFieldValue.getRawValue());
                if(this.infoVoType == InfoVoType.ITEM_INFO){
                    ((ItemInfoVo)this.originalEntityVo).getFields().add(fieldValueVo);
                }
                else if(this.infoVoType == InfoVoType.USER_INFO){
                    ((STUserInfoVo)this.originalEntityVo).getFields().add(fieldValueVo);
                }
            }
        }
        var dateTimeMap = this.originalEntityVo.getDatetimeFields();
        if( dateTimeMap != null && !dateTimeMap.isEmpty()){
            for( var entry : dateTimeMap.entrySet()){
                int fieldId = Integer.parseInt(entry.getKey());
                FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId, moduleId, fieldId,workProjectId);
                IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
                if( oldFieldValue == null)
                    continue;
                oldFieldValue.readValueFromDB(entry.getValue());
                TFieldValueVo fieldValueVo = new TFieldValueVo();
                fieldValueVo.setFieldId(fieldId);
                fieldValueVo.setValue(oldFieldValue.getDisplayValue());
                if(this.infoVoType == InfoVoType.ITEM_INFO){
                    ((ItemInfoVo)this.originalEntityVo).getFields().add(fieldValueVo);
                }
                else if(this.infoVoType == InfoVoType.USER_INFO){
                    ((STUserInfoVo)this.originalEntityVo).getFields().add(fieldValueVo);
                }
            }
        }
    }
    /**
     * 获取业务对象的项目ID
     * Get project ID from business object
     *
     * @param bo 业务对象
     * @return 项目ID
     */
    protected abstract int getProjectId(T bo);
    /**
     * 获取业务对象ID
     * Get ID from business object
     *
     * @param bo 业务对象
     * @return ID
     */
    protected abstract int getId(T bo);

}
