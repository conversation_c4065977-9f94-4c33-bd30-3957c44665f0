package org.dromara.servicetrack.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemChangeText;
import org.dromara.common.mybatis.core.domain.STBaseEntity;


/**
 * 业务对象 item_change_text
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemChangeText.class)
public class ItemChangeTextBo extends BaseChangeTextBo  {
    /**
     * item id
     */
    private Integer itemId;
}
