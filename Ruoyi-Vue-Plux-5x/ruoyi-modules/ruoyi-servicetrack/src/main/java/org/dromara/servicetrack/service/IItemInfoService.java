package org.dromara.servicetrack.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.vo.ListViewFieldVo;
import org.dromara.common.servicetrack.domain.vo.WorkflowTransitionStateVo;
import org.dromara.servicetrack.domain.bo.ItemInfoBo;
import org.dromara.servicetrack.domain.bo.ItemListBo;
import org.dromara.servicetrack.domain.bo.ItemTempInfoBo;
import org.dromara.servicetrack.domain.bo.ItemToDoListBo;
import org.dromara.servicetrack.domain.vo.ItemHistoryModelVo;
import org.dromara.servicetrack.domain.vo.ItemInfoVo;
import org.dromara.servicetrack.domain.vo.ItemListVo;
import org.dromara.servicetrack.domain.vo.ItemToDoListVo;


import java.util.Collection;
import java.util.List;

/**
 * 条目管理 服务层
 *
 * <AUTHOR> fei
 * */
public interface IItemInfoService {
    /**
     * 查询项目
     */
    ItemInfoVo queryById(Long id);

    /**
     * 分页查询Item列表
     */
    TableDataInfo<ItemListVo> selectPageItemList(ItemListBo item, PageQuery pageQuery);

    /**
     * 分页查询Item todo 列表
     */
    ItemToDoListVo selectPageItemToDoList(ItemToDoListBo item, PageQuery pageQuery);
    /**
     * 查询Item详情
     */
    ItemInfoVo getItemDetail(Integer projectId, Integer itemId,Integer workProjectId);

    /**
     * 查询Item template详情
     */
    ItemInfoVo getItemTemplateDetail( Integer projectId, Integer templateId);
    /**
     * 查询Item 下一个状态
     */
    WorkflowTransitionStateVo getItemNextState(Integer projectId, Integer itemId,Integer stateId);
    /**
     * 查询项目列表
     */
    TableDataInfo<ItemInfoVo> queryPageList(ItemInfoBo bo, PageQuery pageQuery);

    /**
     * 查询Item列表
     */
    List<ItemInfoVo> queryList(ItemInfoBo bo);

    /**
     * 新增Item
     */
    Integer insertByBo(ItemInfoBo bo);

    /**
     * 获取预分配的ItemId
     */
    Integer getPreAllocatedItemId(Integer projectId);
    /**
     * 修改Item
     */
    Integer updateByBo(ItemInfoBo bo);

    /**
     * 校验并批量删除Item信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 新增Item template
     */
    Integer insertTemplateByBo(ItemTempInfoBo bo);

    /**
     * 修改Item template
     */
    Integer updateTemplateByBo(ItemTempInfoBo bo);
    /**
     * 校验并批量删除Item template
     */
    Boolean deleteTemplateWithValidByIds(Collection<Long> ids, Boolean isValid);
    /**
     * 获得ListView字段信息
     *  @param projectId 项目ID
     *  @param option 选项：0：可得到字段和选择字段，1：选择字段
     */
    ListViewFieldVo getListviewFields(Integer projectId, Integer option);

    /*
    * 获取条目历史
     */
    ItemHistoryModelVo getItemHistory(Integer projectId, Integer itemId);
}
