package org.dromara.servicetrack.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.vo.ListViewFieldVo;
import org.dromara.servicetrack.domain.UserInfo;
import org.dromara.servicetrack.domain.bo.ItemInfoBo;
import org.dromara.servicetrack.domain.bo.UserInfoBo;
import org.dromara.servicetrack.domain.bo.UserInfoListBo;
import org.dromara.servicetrack.domain.vo.STUserInfoVo;
import org.dromara.servicetrack.domain.vo.UserInfoListVo;

import java.util.Collection;

public interface IUserInfoService {
    /**
     * 分页查询User列表
     */
    TableDataInfo<UserInfoListVo> selectPageUserList(UserInfoListBo bo, PageQuery pageQuery );

    /**
     * 获取User字段详情
     */
    STUserInfoVo getUserInfoDetail(Integer projectId,  Integer userId);
    /**
     * 新增User
     */
    Integer insertByBo(UserInfoBo bo);


    /**
     * 修改User info
     */
    Integer updateByBo(UserInfoBo bo);

    /**
     * 校验并批量删除User信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Integer projectId,Boolean isValid);

    /**
     * 获得ListView字段信息
     *  @param projectId 项目ID
     *  @param option 选项：0：可得到字段和选择字段，1：选择字段
     */
    ListViewFieldVo getListviewFields(Integer projectId, Integer option);
}
