package org.dromara.servicetrack.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 业务对象 base changelog
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseChangelogBo extends STBaseEntity {

    /**
     * ID
     */
    //@NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;
    /**
     * changelog Id
     */
    private Integer changelogId;

    /**
     * log_time
     */
    private Date logTime;

    /**
     * changedById
     */
    private Integer changedById;

    /**
     * changelog Fields
     */
    private List<BaseChangelogFieldBo> changelogFields;
}
