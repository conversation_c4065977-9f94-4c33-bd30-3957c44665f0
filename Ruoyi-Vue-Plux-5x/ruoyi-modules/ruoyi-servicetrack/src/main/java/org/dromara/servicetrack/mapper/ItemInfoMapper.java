package org.dromara.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.ItemInfo;
import org.dromara.servicetrack.domain.bo.ItemInfoBo;
import org.dromara.servicetrack.domain.vo.ItemInfoVo;
import org.dromara.servicetrack.domain.vo.ItemListVo;

import java.util.List;
import java.util.Map;


/**
 * 条目管理 数据层
 *
 * <AUTHOR> fei
 */
public interface ItemInfoMapper extends BaseMapperPlus<ItemInfo, ItemInfoVo>  {

    default LambdaQueryWrapper<ItemInfo> buildWrapper(ItemInfoBo bo) {
        LambdaQueryWrapper<ItemInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, ItemInfo::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, ItemInfo::getProjectId, bo.getProjectId());
        lqw.eq(bo.getItemId() != null, ItemInfo::getItemId, bo.getItemId());
        lqw.like(StringUtils.isNotBlank(bo.getItemTitle()), ItemInfo::getItemTitle, bo.getItemTitle());
        lqw.like(StringUtils.isNotBlank(bo.getDisplayId()), ItemInfo::getDisplayId, bo.getDisplayId());
        lqw.eq(bo.getOwnerId() != null, ItemInfo::getOwnerId, bo.getOwnerId());
        lqw.eq(bo.getStateId() != null, ItemInfo::getStateId, bo.getStateId());
        lqw.eq(bo.getModifiedBy() != null, ItemInfo::getModifiedBy, bo.getModifiedBy());
        lqw.eq(bo.getCreatedBy() != null, ItemInfo::getCreatedBy, bo.getCreatedBy());
        lqw.eq(bo.getTypeId() != null, ItemInfo::getTypeId, bo.getTypeId());
        lqw.eq(bo.getModuleId() != null, ItemInfo::getModuleId, bo.getModuleId());
        return lqw;
    }

    @DataPermission({
        @DataColumn(key = "projectName", value = "u.project_id"),
        @DataColumn(key = "itemTitle", value = "u.item_id")
    })
    Page<ItemListVo> selectPageItemList(@Param("page") Page<ItemInfo> page, @Param(Constants.WRAPPER) Wrapper<ItemInfo> queryWrapper,
                                        @Param("projectId") Integer projectId, @Param("itemSqlSegment") String itemSqlSegment,
                                        @Param("sortFieldId") Integer sortFieldId, @Param("textFieldIds") List<Integer> textFieldIds,
                                        @Param("dateTimeFieldIds") List<Integer> dateTimeFieldIds,@Param("selectionFieldIds") List<Integer> selectionFieldIds);
    /**
     * 获取条目详情
     *
     * @param projectId 项目id
     * @param itemId    条目id
     * @return 条目详情
     */
    ItemInfoVo getItemDetail(@Param("projectId") Integer projectId, @Param("itemId") Integer itemId);
}
