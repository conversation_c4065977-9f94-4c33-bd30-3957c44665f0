package org.dromara.common.core.utils.file;

import org.dromara.common.core.config.RuoYiConfig;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.exception.file.FileNameLengthLimitExceededException;
import org.dromara.common.core.exception.file.FileSizeLimitExceededException;
import org.dromara.common.core.exception.file.InvalidExtensionException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.StringUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Objects;

/**
 * 文件上传工具类
 *
 * <AUTHOR> fei
 */
public class FileUploadUtils {

    /**
     * 默认大小 50M
     */
    public static final long DEFAULT_MAX_SIZE = 50 * 1024 * 1024;

    /**
     * 默认的文件名最大长度 100
     */
    public static final int DEFAULT_FILE_NAME_LENGTH = 100;

    /**
     * 默认上传的地址
     */
    private static String defaultBaseDir = RuoYiConfig.getProfileStatic();

    public static void setDefaultBaseDir(String defaultBaseDir) {
        FileUploadUtils.defaultBaseDir = defaultBaseDir;
    }

    public static String getDefaultBaseDir() {
        return defaultBaseDir;
    }

    /**
     * 以默认配置进行文件上传
     *
     * @param file 上传的文件
     * @return 文件名称
     * @param projectId 项目id
     * @param itemId 条目id
     * @param attachmentId 附件id
     * @throws Exception
     */
    public static String upload(MultipartFile file,Integer projectId,Integer itemId,Integer attachmentId) throws IOException {
        try {
            return upload(getDefaultBaseDir(), file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION, projectId, itemId, attachmentId);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 根据文件路径上传
     *
     * @param baseDir 相对应用的基目录
     * @param file    上传的文件
     * @param projectId 项目id
     * @param itemId 条目id
     * @param attachmentId 附件id
     * @return 文件名称
     * @throws IOException
     */
    public static String upload(String baseDir, MultipartFile file,Integer projectId,Integer itemId,Integer attachmentId) throws IOException {
        try {
            return upload(baseDir, file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION, projectId, itemId, attachmentId);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }
    /**
     * 文件上传
     *
     * @param baseDir            相对应用的基目录
     * @param file               上传的文件
     * @param allowedExtension   上传文件类型
     * @param projectId 项目id
     * @param itemId 条目id
     * @param attachmentId 附件id
     * @return                   返回上传成功的文件名
     * @throws FileSizeLimitExceededException       如果超出最大大小
     * @throws FileNameLengthLimitExceededException 文件名太长
     * @throws IOException                           比如读写文件出错时
     * @throws InvalidExtensionException             文件校验异常
     */
    public static String upload(String baseDir, MultipartFile file, String[] allowedExtension,Integer projectId,Integer itemId,Integer attachmentId)
        throws FileSizeLimitExceededException, IOException, FileNameLengthLimitExceededException,
        InvalidExtensionException {
        int fileNameLength = Objects.requireNonNull(file.getOriginalFilename()).length();
        if (fileNameLength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH) {
            throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
        }

        assertAllowed(file, allowedExtension);

        String fileName = extractFilename(file,projectId,itemId,attachmentId);

        String absPath = getAbsoluteFile(baseDir, fileName).getAbsolutePath();
        file.transferTo(Paths.get(absPath));
        String extension = getExtension(file).toLowerCase();
        if(Arrays.asList(MimeTypeUtils.IMAGE_EXTENSION).contains(extension)) {
            // 如果是图片类型，生成缩略图
            File thumbFile = getAbsoluteFile(baseDir, fileName + MimeTypeUtils.THUMBNAIL_SUFFIX + "." + extension);
            createThumbnail(new File(absPath), thumbFile, 150, 150);
        }
        return getPathFileName(baseDir, fileName);
    }
    /**
     * 生成缩略图
     *
     * @param source      原图片文件
     * @param target      缩略图文件
     * @param targetWidth 目标宽度
     * @param targetHeight 目标高度
     * @throws IOException IO异常
     */
    public static void createThumbnail(File source, File target, int targetWidth, int targetHeight) throws IOException {
        String extension = FilenameUtils.getExtension(source.getName()).toLowerCase();
        if (extension.equals("ico") || extension.equals("tiff") || extension.equals("tif") || extension.equals("svg")) {
            // ICO、TIFF 和 SVG 文件直接复制原文件
            org.apache.commons.io.FileUtils.copyFile(source, target);
            return;
        }
        BufferedImage srcImage = ImageIO.read(source);
        int width = srcImage.getWidth();
        int height = srcImage.getHeight();

        // 计算缩放比例
        double scale = Math.min((double) targetWidth / width, (double) targetHeight / height);
        int scaledWidth = (int) (width * scale);
        int scaledHeight = (int) (height * scale);

        // 创建缩略图
        BufferedImage thumbnail = new BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_INT_RGB);
        thumbnail.createGraphics().drawImage(
            srcImage.getScaledInstance(scaledWidth, scaledHeight, java.awt.Image.SCALE_SMOOTH),
            0, 0, null);

        // 保存缩略图
        ImageIO.write(thumbnail, FilenameUtils.getExtension(source.getName()), target);
    }

    public static boolean isImageFile(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        return fileName != null && fileName.matches("\\S+(\\.(?i)(jpg|jpeg|png|gif|bmp|svg|tiff|tif|webp|ico))$");
    }
    public static String getThumbnailSuffix(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (fileName != null && fileName.matches("\\S+(\\.(?i)(jpg|jpeg|png|gif|bmp|svg|tiff|tif|webp|ico))$")) {
            return MimeTypeUtils.THUMBNAIL_SUFFIX + "." + getExtension(file);
        }
        return "";
    }
    /**
     * 编码文件名
     */
    public static String extractFilename(MultipartFile file,Integer projectId,Integer itemId,Integer attachmentId) {
        String FileName =  StringUtils.format("{}/{}/{}.{}", DateUtils.datePath(),
            FilenameUtils.getBaseName(file.getOriginalFilename()), Seq.getId(Seq.uploadSeqType), getExtension(file));
        if( projectId != null && itemId != null && attachmentId != null) {
            FileName = StringUtils.format("P{}/I{}/F{}/{}", projectId, itemId, attachmentId, file.getOriginalFilename());
        }
        return FileName;
    }
    public static File getAbsoluteFile(String uploadDir, String fileName) throws IOException {
        File desc = new File(uploadDir + File.separator + fileName);

        if (!desc.exists()) {
            if (!desc.getParentFile().exists()) {
                desc.getParentFile().mkdirs();
            }
        }
        return desc;
    }

    public static String getPathFileName(String uploadDir, String fileName) throws IOException {
        int dirLastIndex = RuoYiConfig.getProfileStatic().length() + 1;
        String currentDir = StringUtils.substring(uploadDir, dirLastIndex);
        return Constants.RESOURCE_PREFIX + "/" + currentDir + "/" + fileName;
    }
    /**
     * 文件大小校验
     *
     * @param file            上传的文件
     * @param allowedExtension 允许的扩展名
     * @throws FileSizeLimitExceededException 如果超出最大大小
     * @throws InvalidExtensionException      如果是无效的扩展名
     */
    public static void assertAllowed(MultipartFile file, String[] allowedExtension)
        throws FileSizeLimitExceededException, InvalidExtensionException {
        long size = file.getSize();
        if (size > DEFAULT_MAX_SIZE) {
            throw new FileSizeLimitExceededException(DEFAULT_MAX_SIZE / 1024 / 1024);
        }

        String fileName = file.getOriginalFilename();
        String extension = getExtension(file);
        if (allowedExtension != null && !isAllowedExtension(extension, allowedExtension)) {
            if (allowedExtension == MimeTypeUtils.IMAGE_EXTENSION) {
                throw new InvalidExtensionException.InvalidImageExtensionException(allowedExtension, extension,
                    fileName);
            } else if (allowedExtension == MimeTypeUtils.FLASH_EXTENSION) {
                throw new InvalidExtensionException.InvalidFlashExtensionException(allowedExtension, extension,
                    fileName);
            } else if (allowedExtension == MimeTypeUtils.MEDIA_EXTENSION) {
                throw new InvalidExtensionException.InvalidMediaExtensionException(allowedExtension, extension,
                    fileName);
            } else if (allowedExtension == MimeTypeUtils.VIDEO_EXTENSION) {
                throw new InvalidExtensionException.InvalidVideoExtensionException(allowedExtension, extension,
                    fileName);
            } else {
                throw new InvalidExtensionException(allowedExtension, extension, fileName);
            }
        }
    }
    /**
     * 判断MIME类型是否是允许的MIME类型
     *
     * @param extension        扩展名
     * @param allowedExtension 允许的扩展名数组
     * @return 是否允许
     */
    public static boolean isAllowedExtension(String extension, String[] allowedExtension) {
        for (String str : allowedExtension) {
            if (str.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取文件名的后缀
     *
     * @param file 表单文件
     * @return 后缀名
     */
    public static String getExtension(MultipartFile file) {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (StringUtils.isEmpty(extension)) {
            extension = MimeTypeUtils.getExtension(Objects.requireNonNull(file.getContentType()));
        }
        return extension;
    }
}
