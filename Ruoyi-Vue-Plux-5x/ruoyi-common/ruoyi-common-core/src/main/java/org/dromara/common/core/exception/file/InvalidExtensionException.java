package org.dromara.common.core.exception.file;

import java.io.Serial;

/**
 * 文件名扩展名异常类
 */
public class InvalidExtensionException extends FileException {
    @Serial
    private static final long serialVersionUID = 1L;

    public InvalidExtensionException(String[] allowedExtension, String extension, String fileName){
        super("invalid file extension", new Object[]{allowedExtension, extension, fileName});
    }
    public InvalidExtensionException(String message, String[] allowedExtension, String extension, String fileName) {
        super(message, new Object[]{allowedExtension, extension, fileName});
    }

    public static class InvalidImageExtensionException extends InvalidExtensionException {
        public InvalidImageExtensionException(String[] allowedExtension, String extension, String fileName) {
            super("Invalid image file extension", allowedExtension, extension, fileName);
        }
    }

    public static class InvalidFlashExtensionException extends InvalidExtensionException {
        public InvalidFlashExtensionException(String[] allowedExtension, String extension, String fileName) {
            super("Invalid flash file extension", allowedExtension, extension, fileName);
        }
    }

    public static class InvalidMediaExtensionException extends InvalidExtensionException {
        public InvalidMediaExtensionException(String[] allowedExtension, String extension, String fileName) {
            super("Invalid media file extension", allowedExtension, extension, fileName);
        }
    }

    public static class InvalidVideoExtensionException extends InvalidExtensionException {
        public InvalidVideoExtensionException(String[] allowedExtension, String extension, String fileName) {
            super("Invalid video file extension", allowedExtension, extension, fileName);
        }
    }
}

