package org.dromara.common.core.utils.file;

/**
 * 媒体类型工具类
 *
 * <AUTHOR> fei
 */
public class MimeTypeUtils {
    public static final String IMAGE_PNG = "image/png";

    public static final String IMAGE_JPG = "image/jpg";

    public static final String IMAGE_JPEG = "image/jpeg";

    public static final String IMAGE_BMP = "image/bmp";

    public static final String IMAGE_GIF = "image/gif";
    public static final String IMAGE_WEBP = "image/webp";
    public static final String IMAGE_ICO = "image/ico";
    public static final String IMAGE_SVG = "image/svg";
    public static final String IMAGE_TIF = "image/tif";
    public static final String IMAGE_TIFF = "image/tiff";

    public static final String[] IMAGE_EXTENSION = {"bmp", "gif", "jpg", "jpeg", "png","webp","ico","svg","tif","tiff"};
    public static final String THUMBNAIL_SUFFIX = "_thumb";

    public static final String[] FLASH_EXTENSION = {"swf", "flv"};

    public static final String[] MEDIA_EXTENSION = {"swf", "flv", "mp3", "wav", "wma", "wmv", "mid", "avi", "mpg",
        "asf", "rm", "rmvb"};

    public static final String[] VIDEO_EXTENSION = {"mp4", "avi", "rmvb"};

    public static final String[] DEFAULT_ALLOWED_EXTENSION = {
        // 图片
        "bmp", "gif", "jpg", "jpeg", "png","webp","ico","svg","tif","tiff",
        // word excel powerpoint
        "doc", "docx", "xls", "xlsx", "ppt", "pptx", "html", "htm", "txt","log",
        // 压缩文件
        "rar", "zip", "gz", "bz2",
        // 视频格式
        "mp4", "avi", "rmvb",
        // pdf
        "pdf",
    };

    /**
     * 获取文件扩展名
     * @param prefix
     * @return
     */
    public static String getExtension(String prefix) {
        return switch (prefix) {
            case IMAGE_PNG -> "png";
            case IMAGE_JPG -> "jpg";
            case IMAGE_JPEG -> "jpeg";
            case IMAGE_BMP -> "bmp";
            case IMAGE_GIF -> "gif";
            case IMAGE_WEBP -> "webp";
            case IMAGE_ICO -> "ico";
            case IMAGE_SVG -> "svg";
            case IMAGE_TIF -> "tif";
            case IMAGE_TIFF -> "tiff";
            default -> "";
        };
    }

}
