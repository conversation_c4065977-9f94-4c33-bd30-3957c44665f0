package org.dromara.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 *
 * <AUTHOR> Li
 */

@Data
@Component
@ConfigurationProperties(prefix = "ruoyi")
public class RuoYiConfig {

    /**
     * 项目名称
     */
    private String name;

    /**
     * 版本
     */
    private String version;

    /**
     * 版权年份
     */
    private String copyrightYear;

    /**
     * 上传路径
     */
    private  String  profile;

    private static String profileStatic;

    /**
     * 获取上传路径
     */
    public static String getProfileStatic() {
        return profileStatic;
    }

    /**
     * 设置上传路径
     */
    public void setProfile(String profile) {
        this.profile = profile;
        RuoYiConfig.profileStatic = profile;
    }
    /**
     * 获取上传路径
     */
    public static String getUploadPath() {
        System.out.println(profileStatic);
        return profileStatic + "/upload";
    }
    /**
     * 获取下载路径
     */
    public static String getDownloadPath() {
        System.out.println(profileStatic);
        return profileStatic + "/download";
    }
}
