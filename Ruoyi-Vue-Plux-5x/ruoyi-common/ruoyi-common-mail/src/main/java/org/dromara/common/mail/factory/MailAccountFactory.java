// MailAccountFactory.java
package org.dromara.common.mail.factory;

import cn.hutool.extra.mail.MailAccount;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.mail.config.properties.MailConfigDo;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;

@Component
@Slf4j
public class MailAccountFactory {

    private final ObjectProvider<MailAccount> mailAccountProvider;

    public MailAccountFactory(ObjectProvider<MailAccount> mailAccountProvider) {
        this.mailAccountProvider = mailAccountProvider;
    }

    /**
     * 根据项目名称构建专属 MailAccount
     */
    public MailAccount createMailAccount(MailConfigDo config) {
        if (config == null) {
            log.warn("No mail config found" );
            return null;
        }
        if( !config.getEnabled()){
            log.warn("Mail({}) of project('{}') is not enabled", config.getHost(),config.getProjectName());
            return null;
        }
        MailAccount account = new MailAccount();

        account.setHost(config.getHost());
        account.setPort(config.getPort());
        account.setAuth(config.getAuth());
        account.setFrom(config.getFrom());
        account.setUser(config.getUser());
        account.setPass(config.getPass());
        account.setSocketFactoryPort(config.getPort());
        account.setStarttlsEnable(config.getStarttlsEnable());
        account.setSslEnable(config.getSslEnable());
        if(config.getTimeout() == null)
            config.setTimeout(15000L);
        account.setTimeout(config.getTimeout());
        if(config.getConnectionTimeout() == null)
            config.setConnectionTimeout(5000L);
        account.setConnectionTimeout(config.getConnectionTimeout());

        return account;
    }
}
