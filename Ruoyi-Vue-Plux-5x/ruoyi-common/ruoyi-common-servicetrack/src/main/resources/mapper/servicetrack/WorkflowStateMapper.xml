<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.WorkflowStateMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.WorkflowStateVo" id="WorkflowStateInfoResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="stateId" column="state_id"/>
        <result property="stateName" column="state_name"/>
        <result property="stateOptionId" column="state_option_id"/>
    </resultMap>
</mapper>
