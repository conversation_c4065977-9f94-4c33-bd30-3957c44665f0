<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.NotificationRecipientMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.NotificationRecipientVo" id="NotificationRecipientResult">
        <id property="keyId" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="ruleId" column="rule_id"/>
        <result property="userType" column="user_type"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
    </resultMap>

    <sql id="selectNotificationRecipientVo">
        select nr.key_id, nr.project_id, nr.rule_id, nr.user_type, nr.user_id,
               u.user_name, u.nick_name
        from notification_recipient nr
        left join sys_user u on u.external_user_id = nr.user_id
    </sql>

    <select id="selectByProjectAndRule" resultMap="NotificationRecipientResult">
        <include refid="selectNotificationRecipientVo"/>
        where nr.project_id = #{projectId} and nr.rule_id = #{ruleId}
        order by nr.user_type, nr.user_id
    </select>

    <select id="selectByProjectId" resultMap="NotificationRecipientResult">
        <include refid="selectNotificationRecipientVo"/>
        where nr.project_id = #{projectId}
        order by nr.rule_id, nr.user_type, nr.user_id
    </select>

    <select id="selectByUserType" resultMap="NotificationRecipientResult">
        <include refid="selectNotificationRecipientVo"/>
        where nr.project_id = #{projectId} and nr.user_type = #{userType}
        order by nr.rule_id, nr.user_id
    </select>

    <select id="selectByUserId" resultMap="NotificationRecipientResult">
        <include refid="selectNotificationRecipientVo"/>
        where nr.project_id = #{projectId} and nr.user_id = #{userId}
        order by nr.rule_id, nr.user_type
    </select>

</mapper>
