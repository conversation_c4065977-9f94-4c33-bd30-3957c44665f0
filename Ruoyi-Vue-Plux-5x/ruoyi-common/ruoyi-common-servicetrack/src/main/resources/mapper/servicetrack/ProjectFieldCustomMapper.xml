<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectFieldCustomMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.dromara.common.servicetrack.domain.ProjectFieldCustom">
        <id column="id" property="keyId" />
        <result column="project_id" property="projectId" />
        <result column="field_id" property="fieldId" />
        <result column="field_name" property="fieldName" />
        <result column="module_id" property="moduleId" />
        <result column="field_type" property="fieldType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        key_id, project_id, field_id, field_name, module_id, field_type
    </sql>
</mapper>
