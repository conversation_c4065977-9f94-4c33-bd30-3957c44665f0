<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectInfoMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.ProjectInfoVo" id="ProjectInfoResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="projectName" column="project_name"/>
        <result property="projectType" column="project_type"/>
        <result property="projectKey" column="project_key"/>
        <result property="isActive" column="isactive"/>
        <result property="baseProjectId" column="baseproject_id"/>
    </resultMap>
    <select id="getEmailSetting"  resultType="java.lang.String">
        select setting_content
        from project_setting
        where project_id = #{projectId} and setting_id=#{settingId}
    </select>
</mapper>
