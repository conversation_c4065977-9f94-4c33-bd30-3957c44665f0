<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.NotificationRuleMapper">
    <!-- 查询所有 -->
    <select id="selectAll" resultType="org.dromara.common.servicetrack.domain.vo.NotificationRuleVo">
        SELECT * FROM notification_rule
    </select>

    <!-- 根据主键查询 -->
    <select id="selectByPk" parameterType="map" resultType="org.dromara.common.servicetrack.domain.vo.NotificationRuleVo">
        SELECT * FROM notification_rule WHERE project_id = #{projectId} AND rule_id = #{ruleId}
    </select>

    <!-- 插入数据 -->
    <insert id="insert">
        INSERT INTO notification_rule (key_id, project_id, rule_id, object_id, rule_name, rule_type, is_active)
        VALUES (#{bo.keyId}, #{bo.projectId}, #{bo.ruleId}, #{bo.objectId}, #{bo.ruleName}, #{bo.ruleType}, #{bo.isActive})
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        UPDATE notification_rule SET key_id = #{bo.keyId}, object_id = #{bo.objectId}, rule_name = #{bo.ruleName}, rule_type = #{bo.ruleType}, is_active = #{bo.isActive} WHERE project_id = #{bo.projectId} AND rule_id = #{bo.ruleId}
    </update>

    <!-- 删除数据 -->
    <delete id="delete">
        DELETE FROM notification_rule WHERE project_id = #{projectId} AND rule_id = #{ruleId}
    </delete>
</mapper>