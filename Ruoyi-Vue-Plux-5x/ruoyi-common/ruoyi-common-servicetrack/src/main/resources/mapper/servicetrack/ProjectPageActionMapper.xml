<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectPageActionMapper">
    <resultMap id="ProjectPageActionResult" type="org.dromara.common.servicetrack.domain.vo.ProjectPageActionVo">
        <result property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="itemtypeId" column="itemtype_id"/>
        <result property="pageId" column="page_id"/>
        <result property="actionId" column="action_id"/>
        <result property="pageOrder" column="page_order"/>
        <result property="typeId" column="type_Id"/>
        <result property="typeName" column="type_name"/>
        <result property="transitionId" column="transition_id"/>
    </resultMap>

    <sql id="selectProjectPageActionVo">
        select key_id, project_id, itemtype_id, page_id, action_id, page_order from project_page_action
    </sql>

    <select id="selectVoById" resultMap="ProjectPageActionResult">
        <include refid="selectProjectPageActionVo"/>
        where project_id = #{projectId} and itemtype_id = #{itemtypeId} and page_id = #{pageId} and action_id = #{actionId}
    </select>
    <select id="selectPageActionList" resultMap="ProjectPageActionResult">
        select pa.key_id, pa.page_id, pa.action_id, pa.page_order,it.type_id,it.type_name, it.transition_id
        from project_page_action pa
        left join project_itemtype it on pa.project_id = it.project_id and pa.itemtype_id = it.type_id
        where pa.project_id = #{projectId}
    </select>
</mapper>
