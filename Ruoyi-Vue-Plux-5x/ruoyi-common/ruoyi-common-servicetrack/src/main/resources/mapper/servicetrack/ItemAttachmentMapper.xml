<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ItemAttachmentMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.ItemAttachmentVo" id="ItemAttachmentInfoResult">
        <id property="id" column="key_id"/>
        <result property="attachmentId" column="attachment_id"/>
        <result property="ossId" column="oss_id"/>
        <result property="attachmentName" column="attachment_name"/>
        <result property="fileName" column="file_name"/>
        <result property="url" column="url"/>
        <result property="thumbnailUrl" column="thumbnail_url"/>
        <result property="createdTime" column="created_time"/>
        <result property="createdBy" column="createdBy"/>
    </resultMap>
    <select id="selectItemAttachmentList" resultMap="ItemAttachmentInfoResult">
        SELECT i.key_id, i.attachment_id, i.oss_id, i.attachment_name, oss.file_name, oss.url, oss.thumbnail_url, i.created_time, i.created_by
        FROM item_attachment i
        LEFT JOIN sys_oss oss ON oss.oss_id = i.oss_id
        WHERE project_id = #{projectId}
        AND item_id = #{itemId}
       </select>
    <select id="selectItemAttachmentIdsByOssIds" resultType="java.lang.Long">
        <!-- 根据ossId查询附件id -->
        SELECT i.key_id
        FROM item_attachment i
        WHERE i.oss_id IN
        <foreach collection="ossIds" item="ossId" open="(" separator="," close=")">
            #{ossId}
        </foreach>
    </select>
</mapper>
