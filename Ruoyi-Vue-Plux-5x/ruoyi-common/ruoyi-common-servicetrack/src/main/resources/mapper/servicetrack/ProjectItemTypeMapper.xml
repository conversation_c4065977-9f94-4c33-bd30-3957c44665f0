<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectItemTypeMapper">
    <resultMap id="BaseResultMap" type="org.dromara.common.servicetrack.domain.ProjectItemType">
        <id column="key_id" property="keyId" />
        <result column="project_id" property="projectId" />
        <result column="type_id" property="typeId" />
        <result column="type_name" property="typeName" />
        <result column="transition_id" property="transitionId" />
    </resultMap>

    <sql id="Base_Column_List">
        key_id, project_id, type_id, type_name, transition_id
    </sql>
</mapper>
