<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectMemberMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.ProjectMemberVo" id="ProjectMemberResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="userId" column="user_id"/>
        <result property="userType" column="user_type"/>
        <result property="accountTypeId" column="accounttype_id"/>
        <result property="accountTypeName" column="accounttype_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="userName" column="user_name"/>
    </resultMap>
    <resultMap type="org.dromara.common.servicetrack.domain.vo.SysUserSimpleInfoVo" id="userListResult">
        <id property="userId" column="user_id"/>
        <result property="externalUserId" column="external_user_id"/>
        <result property="stUserType" column="st_user_type"/>
        <result property="nickName" column="nick_name"/>
        <result property="userName" column="user_name"/>
    </resultMap>


    <sql id="selectMemberListBase">
        select u.nick_name, u.user_name,pm.key_id, pm.user_id, pm.user_type, pm.accounttype_id, at.accounttype_name
        from project_member pm
        left join sys_user u on u.external_user_id = pm.user_id
        left join project_accounttype at on (at.project_id = pm.project_id and at.accounttype_id = pm.accounttype_id)
        where pm.project_id = #{projectId}
    </sql>

    <select id="selectMemberList" parameterType="Integer" resultMap="ProjectMemberResult">
        <include refid="selectMemberListBase"/>
    </select>

    <select id="selectMemberListByPage" resultMap="ProjectMemberResult">
        <include refid="selectMemberListBase"/>
    </select>
    <select id="selectUserList" resultMap="userListResult">
        select u.user_id,u.nick_name, u.user_name, u.external_user_id, u.st_user_type
        from sys_user u
        where u.del_flag = 0
    </select>
    <select id="getDeptNameById" resultType="String">
        select CASE WHEN del_flag = '0' THEN dept_name WHEN del_flag = '1' THEN CONCAT('(', dept_name, ')') END from sys_dept where dept_id = #{deptId}
    </select>
</mapper>
