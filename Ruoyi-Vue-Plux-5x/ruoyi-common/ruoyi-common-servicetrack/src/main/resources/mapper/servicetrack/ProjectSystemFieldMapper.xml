<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectSystemFieldMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.ProjectFieldVo" id="ProjectFieldResult">
        <id property="id" column="key_id"/>
        <result property="fieldId" column="field_id"/>
        <result property="fieldName" column="field_name"/>
        <result property="fieldType" column="field_type"/>
        <result property="moduleId" column="module_id"/>
    </resultMap>
    <select id="selectFieldList" resultMap="ProjectFieldResult">
        SELECT  key_id, field_id,field_name,field_type, module_id FROM project_field_system ${ew.getCustomSqlSegment}
        UNION ALL
        SELECT key_id, field_id,field_name,field_type, module_id FROM project_field_custom ${ew.getCustomSqlSegment}
    </select>
</mapper>
