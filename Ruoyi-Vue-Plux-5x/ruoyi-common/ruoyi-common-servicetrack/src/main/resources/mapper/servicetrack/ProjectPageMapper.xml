<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectPageMapper">
    <resultMap id="ProjectPageResult" type="org.dromara.common.servicetrack.domain.ProjectPage">
        <result property="keyId" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="pageId" column="page_id"/>
        <result property="pageName" column="page_name"/>
        <result property="moduleId" column="module_id"/>
    </resultMap>

    <sql id="selectProjectPageVo">
        select key_id, project_id, page_id, page_name, module_id from project_page
    </sql>

    <select id="selectVoById" resultMap="ProjectPageResult">
        <include refid="selectProjectPageVo"/>
        where project_id = #{projectId} and page_id = #{pageId}
    </select>
</mapper>