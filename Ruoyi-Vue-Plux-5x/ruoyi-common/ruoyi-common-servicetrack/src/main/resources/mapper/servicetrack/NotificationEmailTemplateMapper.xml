<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.NotificationEmailTemplateMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.NotificationEmailTemplateVo" id="NotificationEmailTemplateResult">
        <id property="keyId" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="templateId" column="template_id"/>
        <result property="emailSubject" column="email_subject"/>
        <result property="emailBody" column="email_body"/>
    </resultMap>

    <sql id="selectNotificationEmailTemplateVo">
        select key_id, project_id, template_id, email_subject, email_body
        from notification_email_template
    </sql>

    <select id="selectByProjectAndTemplate" resultMap="NotificationEmailTemplateResult">
        <include refid="selectNotificationEmailTemplateVo"/>
        where project_id = #{projectId} and template_id = #{templateId}
    </select>

    <select id="selectByProjectId" resultMap="NotificationEmailTemplateResult">
        <include refid="selectNotificationEmailTemplateVo"/>
        where project_id = #{projectId}
        order by template_id
    </select>

</mapper>
