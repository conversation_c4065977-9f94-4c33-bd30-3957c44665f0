package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ProjectPageAction;

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectPageAction.class)
public class ProjectPageActionBo extends STBaseEntity {
    private Long id;

    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    @NotNull(message = "项目类型ID不能为空")
    private Integer itemTypeId;

    @NotNull(message = "页面ID不能为空")
    private Integer pageId;

    @NotNull(message = "操作ID不能为空")
    private Integer actionId;

    private Integer pageOrder;
}
