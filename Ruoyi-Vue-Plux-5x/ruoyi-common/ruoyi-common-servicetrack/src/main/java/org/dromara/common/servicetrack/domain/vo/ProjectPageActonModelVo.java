package org.dromara.common.servicetrack.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class ProjectPageActonModelVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * item type id
     */
    private Integer typeId;
    /**
     * item type name
     */
    private String typeName;
    /**
     * transition id
     */
    private Integer transitionId;

    List<ProjectPageActionInfoVo> itemTypePageActions;
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ProjectPageActionInfoVo extends STBaseEntity{

        /**
         * page id
         */
        private Integer pageId;
        /**
         * action id
         */
        private Integer actionId;
        /**
         * page order
         */
        private Integer pageOrder;
    }
}
