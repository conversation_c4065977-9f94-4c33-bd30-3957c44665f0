package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ProjectAccountType;
import org.dromara.common.servicetrack.domain.bo.ProjectAccountTypeBo;
import org.dromara.common.servicetrack.domain.vo.ProjectAccountTypeVo;

/**
 * 项目账户类型 数据层
 *
 * <AUTHOR>
 */
public interface ProjectAccountTypeMapper extends BaseMapperPlus<ProjectAccountType, ProjectAccountTypeVo> {

    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ProjectAccountType> buildWrapper(ProjectAccountTypeBo bo) {
        LambdaQueryWrapper<ProjectAccountType> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProjectId() != null, ProjectAccountType::getProjectId, bo.getProjectId());
        lqw.eq(bo.getAccountTypeId() != null, ProjectAccountType::getAccountTypeId, bo.getAccountTypeId());
        lqw.eq(bo.getTypeId() != null, ProjectAccountType::getTypeId, bo.getTypeId());
        lqw.like(StringUtils.isNotBlank(bo.getAccounttypeName()), ProjectAccountType::getAccounttypeName, bo.getAccounttypeName());
        return lqw;
    }
}
