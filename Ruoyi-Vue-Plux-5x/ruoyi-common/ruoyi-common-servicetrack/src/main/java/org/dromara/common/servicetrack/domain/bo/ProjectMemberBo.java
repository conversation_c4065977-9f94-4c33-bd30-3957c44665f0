package org.dromara.common.servicetrack.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ProjectMember;

import java.util.List;

/**
 * 事件业务对象 project_member
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectMember.class)
public class ProjectMemberBo extends STBaseEntity{

    /**
     * ID
     */
    //@NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    @Min(value = 1, message = "projectId 必须大于 0")
    private Integer projectId;

    /**
     * user id
     */
    @NotNull(message = "userId不能为空")
    private Integer userId;

    /**
     * user type
     **/
    private Integer userType;

    /**
     * account type id
     **/
    private Integer accountTypeId;

}

