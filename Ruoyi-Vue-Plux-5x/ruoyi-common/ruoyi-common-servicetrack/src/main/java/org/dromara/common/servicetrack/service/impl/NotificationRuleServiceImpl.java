package org.dromara.common.servicetrack.service.impl;

import java.util.List;

import org.dromara.common.servicetrack.domain.bo.NotificationRuleBo;
import org.dromara.common.servicetrack.domain.vo.NotificationRuleVo;
import org.dromara.common.servicetrack.mapper.NotificationRuleMapper;
import org.dromara.common.servicetrack.service.INotificationRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * notification_rule表的service实现类
 */
@Service
public class NotificationRuleServiceImpl implements INotificationRuleService {
    @Autowired
    private NotificationRuleMapper mapper;

    /**
     * 查询notification_rule表的所有数据
     *
     * @return 返回notification_rule表的所有数据
     */
    @Override
    public List<NotificationRuleVo> selectAll() {
        return mapper.selectAll();
    }

    /**
     * 根据主键查询notification_rule表的数据
     *
     * @param projectId 主键值
     * @param ruleId    主键值
     * @return 返回主键对应的数据
     */
    @Override
    public NotificationRuleVo selectByPk(Integer projectId, Integer ruleId) {
        return mapper.selectByPk(projectId, ruleId);
    }

    /**
     * 插入数据到notification_rule表
     *
     * @param bo 需要插入的数据
     * @return 返回插入数据影响的行数
     */
    @Override
    public int insertByBo(NotificationRuleBo bo) {
        return mapper.insertByBo(bo);
    }

    /**
     * 更新notification_rule表中的数据
     *
     * @param bo 需要更新的数据
     * @return 返回更新数据影响的行数
     */
    @Override
    public int updateByBo(NotificationRuleBo bo) {
        return mapper.updateByBo(bo);
    }


}
