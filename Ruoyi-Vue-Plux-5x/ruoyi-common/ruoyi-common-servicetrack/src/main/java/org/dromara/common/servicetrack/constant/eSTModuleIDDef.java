package org.dromara.common.servicetrack.constant;

public enum eSTModuleIDDef implements IValueEnum{
    Incident(1),
    Knowledge(2),
    Asset(3),
    UserInfo(4);

    private final int value;
    @Override
    public int getValue() {
        return value;
    }
    /*
     *构造函数在定义常量时自动调用
     */
    eSTModuleIDDef(int value) {
        this.value = value;
    }

    public static eSTModuleIDDef from(Integer value) {
        return IValueEnum.valueOf(eSTModuleIDDef.class, value);
    }
}
