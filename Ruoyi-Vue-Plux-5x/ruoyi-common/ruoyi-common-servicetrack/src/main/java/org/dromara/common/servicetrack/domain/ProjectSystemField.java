package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *  project_field_system
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_field_system")
public class ProjectSystemField extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * field id
     */
    @TableField(value = "field_id")
    private Integer fieldId;

    /**
     * field name
     */
    @TableField(value = "field_name")
    private String fieldName;

    /**
     * field type
     */
    @TableField(value = "field_type")
    private Integer fieldType;

    /**
     * module id
     */
    @TableField(value = "module_id")
    private Integer moduleId;

    /**
     * field default name
     */
    @TableField(value = "field_defaultname")
    private String fieldDefaultName;
}
