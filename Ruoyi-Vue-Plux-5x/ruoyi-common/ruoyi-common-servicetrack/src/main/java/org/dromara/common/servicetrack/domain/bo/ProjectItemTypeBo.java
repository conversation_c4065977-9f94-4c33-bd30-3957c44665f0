package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ProjectItemType;

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectItemType.class)
public class ProjectItemTypeBo extends STBaseEntity {
    private Long id;

    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    @NotNull(message = "类型ID不能为空")
    private Integer typeId;

    @NotNull(message = "类型名称不能为空")
    @Xss(message = "类型名称不能包含脚本字符")
    @NotBlank(message = "类型名称不能为空")
    @Size(min = 1, max = 200, message = "类型名称的长度不能超过{max}")
    private String typeName;

    private Integer transitionId;
}
