package org.dromara.common.servicetrack.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectPageActionBo;
import org.dromara.common.servicetrack.domain.bo.ProjectPageActionSortBo;
import org.dromara.common.servicetrack.domain.bo.ProjectSortBinderBo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageActionVo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageActonModelVo;

import java.util.Collection;
import java.util.List;

public interface IProjectPageActionService {
    /**
     * 查询项目页面操作列表
     *
     * @return 项目页面操作
     */
    ProjectPageActionVo queryById(Long keyId);
    /**
     * 查询项目页面操作列表
     *
     * @return 项目页面操作列表
     */
    TableDataInfo<ProjectPageActionVo> queryPageList(Integer projectId, PageQuery pageQuery);
    /**
     * 查询项目页面操作列表
     *
     * @return 项目页面操作列表
     */
    List<ProjectPageActonModelVo> selectPageActionModelList(Integer projectId);
    /**
     * 查询项目页面操作列表
     *
     * @return 项目页面操作列表
     */
    List<ProjectPageActionVo>  selectPageActionList(Integer projectId);
    /**
     * 新增项目页面操作
     *
     * @return 项目页面操作Id
     */
    Integer insertByBo(ProjectPageActionBo bo);
    /**
     * 修改项目页面操作
     *
     * @return 项目页面操作Id
     */
    Integer updateByBo(ProjectPageActionBo bo);
    /**
     * 删除项目页面操作
     *
     * @param keyIds 主键串
     */
    Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid);

    /**
     * 排序项目页面顺序
     *
     */
    Boolean sortPageAction(ProjectPageActionSortBo bo);
}
