package org.dromara.common.servicetrack.domain.vo;

import java.io.Serial;
import java.io.Serializable;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ItemAttachment;

/**
 * notification_rule表的vo类
 * <AUTHOR>
 */
@Data
@AutoMapper(target = ItemAttachment.class)
public class NotificationRuleVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * key_id
     */
    private Long id;

    /**
     * project_id
     */
    private Integer projectId;

    /**
     * rule_id
     */
    private Integer ruleId;

    /**
     * object_id
     */
    private Integer objectId;

    /**
     * rule_name
     */
    private String ruleName;

    /**
     * rule_type
     */
    private Integer ruleType;

    /**
     * is_active
     */
    private Integer isActive;
}
