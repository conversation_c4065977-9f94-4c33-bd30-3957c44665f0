package org.dromara.common.servicetrack.service.impl;

import lombok.RequiredArgsConstructor;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.domain.vo.ItemAttachmentVo;
import org.dromara.common.servicetrack.mapper.ItemAttachmentMapper;
import org.dromara.common.servicetrack.service.IItemAttachmentService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Item 附件 服务层实现
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class ItemAttachmentServiceImpl implements IItemAttachmentService {
    private final ItemAttachmentMapper baseMapper;
    private final TableSequenceManager tableSequenceManager;
    @Override
    public List<ItemAttachmentVo> selectItemAttachmentList(Integer projectId, Integer itemId) {
        return baseMapper.selectItemAttachmentList(projectId,itemId);
    }
//    @Override
//    public List<Long> selectItemAttachmentIdsByOssIds(Collection<?> ossIdsList) {
//        return baseMapper.selectItemAttachmentIdsByOssIds(ossIdsList);
//    }
}
