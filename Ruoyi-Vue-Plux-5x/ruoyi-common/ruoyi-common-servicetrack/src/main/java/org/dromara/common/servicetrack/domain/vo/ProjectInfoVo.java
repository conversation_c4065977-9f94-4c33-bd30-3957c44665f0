package org.dromara.common.servicetrack.domain.vo;

import cn.hutool.extra.mail.MailAccount;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectInfo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 项目视图对象 project_info
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = ProjectInfo.class)
public class ProjectInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * project id
     */
  //  @ExcelProperty(value = "项目ID")
    private Integer projectId;

    /**
     * Project type
     */

    private  Integer projectType;

    /**
     * Project name
     */
    private String projectName;

    /*
     * Project alias
     */
    private String alias;

    /**
     * Project key
     */
    private String projectKey;

    /**
     * Is active
     */
    private Integer isActive;

    /**
     * base project id
     */
    private Integer baseProjectId;

    private List<ProjectInfoVo> children;

    @JsonIgnore
    private MailAccount mailAccount;
}
