package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_itemtype")
public class ProjectItemType extends STBaseEntity {
    @TableId(value = "key_id")
    private Long id;

    @TableField(value = "project_id")
    private Integer projectId;

    @TableField(value = "type_id")
    private Integer typeId;

    @TableField(value = "type_name")
    private String typeName;

    @TableField(value = "transition_id")
    private Integer transitionId;
}
