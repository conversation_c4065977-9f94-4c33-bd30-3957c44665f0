package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jodd.introspector.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ProjectInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectInfoBo;
import org.dromara.common.servicetrack.domain.vo.ProjectInfoVo;
import org.dromara.common.servicetrack.domain.ProjectAccountType;
import org.dromara.common.servicetrack.domain.bo.ProjectAccountTypeBo;
import org.dromara.common.servicetrack.domain.vo.ProjectAccountTypeVo;

/**
 * 项目管理 数据层
 *
 * <AUTHOR> f<PERSON>
 */
//@Mapper
public interface ProjectInfoMapper extends BaseMapperPlus<ProjectInfo, ProjectInfoVo> {

    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ProjectInfo> buildWrapper(ProjectInfoBo bo) {
        LambdaQueryWrapper<ProjectInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, ProjectInfo::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, ProjectInfo::getProjectId, bo.getProjectId());
        lqw.like(StringUtils.isNotBlank(bo.getProjectName()), ProjectInfo::getProjectName, bo.getProjectName());
        lqw.eq(StringUtils.isNotBlank(bo.getProjectKey()), ProjectInfo::getProjectKey, bo.getProjectKey());
        lqw.eq(bo.getProjectType() != null, ProjectInfo::getProjectType, bo.getProjectType());
        lqw.eq(bo.getBaseProjectId() != null, ProjectInfo::getBaseProjectId, bo.getBaseProjectId());
        lqw.eq(bo.getIsActive() != null, ProjectInfo::getIsActive, bo.getIsActive());
        return lqw;
    }

    /**
     * 获取项目账户类型查询包装器
     */
    default LambdaQueryWrapper<ProjectAccountType> buildProjectAccountTypeWrapper(ProjectAccountTypeBo bo) {
        LambdaQueryWrapper<ProjectAccountType> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProjectId() != null, ProjectAccountType::getProjectId, bo.getProjectId());
        lqw.eq(bo.getAccountTypeId() != null, ProjectAccountType::getAccountTypeId, bo.getAccountTypeId());
        lqw.eq(bo.getTypeId() != null, ProjectAccountType::getTypeId, bo.getTypeId());
        lqw.like(StringUtils.isNotBlank(bo.getAccounttypeName()), ProjectAccountType::getAccounttypeName, bo.getAccounttypeName());
        return lqw;
    }
    String getEmailSetting(@Param("projectId") Integer projectId,@Param("settingId") Integer settingId);
}
