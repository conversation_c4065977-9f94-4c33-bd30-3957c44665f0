package org.dromara.common.servicetrack.sequence.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;

import java.util.List;
import java.util.Map;

/**
 * 表序列号Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface TableSequenceMapper {

    /**
     * 获取多个表的最大序列号
     *
     * @param tableInfos 表信息列表，每个元素是一个Map，包含tableName和sequenceColumn
     * @return 表名和最大序列号的映射
     */
    List<Map<String, Object>> getMaxSequences(@Param("tableInfos") List<Map<String, String>> tableInfos);

    /**
     * 获取指定父ID下的最大序列号
     */
    @InterceptorIgnore(tenantLine = "true")
    int getMaxSequenceByParent(@Param("tableName") String tableName,
                              @Param("sequenceColumn") String sequenceColumn,
                              @Param("parentIdColumn") String parentIdColumn,
                              @Param("parentId") int parentId);

    /**
     * 批量获取多个表中所有父ID及其对应的最大序列号
     *
     * @param tableInfos 表信息列表，每个元素是一个Map，包含tableName、sequenceColumn和parentIdColumn
     * @return 表名、父ID和最大序列号的映射列表
     */
    List<Map<String, Object>> getAllParentMaxSequences(@Param("tableInfos") List<Map<String, String>> tableInfos);
}
