package org.dromara.common.servicetrack.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectPageBo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageVo;

import java.util.Collection;
import java.util.List;

public interface IProjectPageService {
    ProjectPageVo queryById(Long keyId);
    TableDataInfo<ProjectPageVo> queryPageList(Integer projectId, PageQuery pageQuery);
    List<ProjectPageVo> selectPageList(Integer projectId);
    Integer insertByBo(ProjectPageBo bo);
    Integer updateByBo(ProjectPageBo bo);
    Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid);
}
