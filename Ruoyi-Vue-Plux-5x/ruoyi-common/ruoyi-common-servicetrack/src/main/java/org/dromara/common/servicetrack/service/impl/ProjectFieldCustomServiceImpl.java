package org.dromara.common.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.ProjectFieldCustom;
import org.dromara.common.servicetrack.domain.ProjectPageField;
import org.dromara.common.servicetrack.domain.bo.ProjectFieldCustomBo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldCustomVo;
import org.dromara.common.servicetrack.mapper.ProjectFieldCustomMapper;
import org.dromara.common.servicetrack.mapper.ProjectPageFieldMapper;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.service.IProjectFieldCustomService;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 项目自定义字段 服务层实现
 *
 * <AUTHOR> Fei
 */
@RequiredArgsConstructor
@Service
public class ProjectFieldCustomServiceImpl implements IProjectFieldCustomService {
    private final ProjectFieldCustomMapper baseMapper;
    private final TableSequenceManager tableSequenceManager;
    private final ProjectPageFieldMapper projectPageFieldMapper;

    /**
     * 查询项目自定义字段
     */
    @Override
    public ProjectFieldCustomVo queryById(Long keyId) {
        return baseMapper.selectVoById(keyId);
    }

    /**
     * 查询项目自定义字段列表
     */
    @Override
    public TableDataInfo<ProjectFieldCustomVo> queryPageList(Integer projectId, PageQuery pageQuery) {
        ProjectFieldCustomBo bo = new ProjectFieldCustomBo();
        bo.setProjectId(projectId);
        return TableDataInfo.<ProjectFieldCustomVo>build((com.baomidou.mybatisplus.core.metadata.IPage<ProjectFieldCustomVo>) baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(bo)));
    }

    @Override
    public List<ProjectFieldCustomVo> queryList(Integer projectId) {
        ProjectFieldCustomBo bo = new ProjectFieldCustomBo();
        bo.setProjectId(projectId);
        return baseMapper.selectVoList(this.buildQueryWrapper(bo));
    }

    private Wrapper<ProjectFieldCustom> buildQueryWrapper(ProjectFieldCustomBo bo) {
        QueryWrapper<ProjectFieldCustom> wrapper = Wrappers.query();
        wrapper.eq(bo.getProjectId() != null, "project_id", bo.getProjectId());
        wrapper.eq(bo.getFieldId() != null, "field_id", bo.getFieldId());
        wrapper.like(StringUtils.isNotBlank(bo.getFieldName()), "field_name", bo.getFieldName());
        wrapper.eq(bo.getModuleId() != null, "module_id", bo.getModuleId());
        wrapper.eq(bo.getFieldType() != null, "field_type", bo.getFieldType());
        return wrapper;
    }

    /**
     * 新增项目自定义字段
     */
    @Override
    public Integer insertByBo(ProjectFieldCustomBo bo) {
        int fieldId  = tableSequenceManager.getNextSequence(SequenceTable.Project_Field_Custom,bo.getProjectId());
        bo.setFieldId(fieldId);
        ProjectFieldCustom add = MapstructUtils.convert(bo, ProjectFieldCustom.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag ? add.getFieldId() : 0;
    }

    /**
     * 修改项目自定义字段
     */
    @Override
    public Integer updateByBo(ProjectFieldCustomBo bo) {
        ProjectFieldCustom update = MapstructUtils.convert(bo, ProjectFieldCustom.class);
        return baseMapper.updateById(update) > 0 ? bo.getFieldId() : 0;
    }

    /**
     * 批量删除项目自定义字段
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        //check if these fields are used in project_page_field table
        // First get the field_ids from ProjectFieldCustom table
        var customFields = baseMapper.selectVoByIds(keyIds);
        List<Integer> fieldIds = customFields.stream().map(ProjectFieldCustomVo::getFieldId).toList();
        if (fieldIds.isEmpty()) {
            return true;
        }
        QueryWrapper<ProjectPageField> wrapper = new QueryWrapper<>();
        wrapper.eq("project_id", customFields.get(0).getProjectId());
        wrapper.in("field_id", fieldIds);
        if (projectPageFieldMapper.selectCount(wrapper) > 0) {
            throw new ServiceException("有些字段正在使用中,不能删除！");
        }
        return baseMapper.deleteByIds(keyIds) > 0;
    }
}
