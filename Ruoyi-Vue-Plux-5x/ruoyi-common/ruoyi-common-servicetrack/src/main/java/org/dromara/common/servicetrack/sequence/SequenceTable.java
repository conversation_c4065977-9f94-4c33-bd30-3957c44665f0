package org.dromara.common.servicetrack.sequence;

/**
 * 需要序列号的表枚举
 *
 * <AUTHOR>
 */
public enum SequenceTable {

    /**
     * 用户表(sys_user)
     */
    USER,
    /**
     * 项目表(project_info)
     */
     PROJECT,

    /**
     * 条目表(item_info)
     */
    Item_Info,

    /**
     * 条目changelog(item_changelog)
     */
    Item_Changelog,

    /**
     * 条目history(item_history)
     */
    Item_History,

    /**
     * 条目附件(item_attachment)
     */
    Item_Attachment,

    /**
     * 项目账户(project_accountType)
     */
    Project_AccountType,

    /**
     * 项目分组(project_group)
     */
    Project_Group,

    /**
     * 项目自定义字段(project_field_custom)
     */
    Project_Field_Custom,

    /**
     * 项目Item 类型(project_item_type)
     */
    Project_Item_Type,
    /**
     * 项目自定义页面(project_page)
     */
    Project_Page,

    /**
     * User info changelog(user_info_changelog)
     */
    UserInfo_Changelog,
}
