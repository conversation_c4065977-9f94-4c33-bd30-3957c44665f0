package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
/**
 * 项目对象 Project_Member
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_member")
public class ProjectMember  extends STBaseEntity {
     /** ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * user id
     */
    @TableField(value = "user_id")
    private Integer userId;

    /**
     * user type
     **/
    @TableField(value = "user_type")
    private Integer userType;

    /**
     * account type id
     **/
    @TableField(value = "accounttype_id")
    private Integer accountTypeId;
}
