package org.dromara.common.servicetrack.service;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.servicetrack.domain.bo.ProjectFieldSelectionBo;
import org.dromara.common.servicetrack.domain.bo.ProjectPageFieldBo;
import org.dromara.common.servicetrack.domain.bo.WorkflowStateBo;
import org.dromara.common.servicetrack.domain.bo.WorkflowTransitionBo;
import org.dromara.common.servicetrack.domain.vo.*;

import java.util.List;
/**
 * 项目数据 服务层
 * */
public interface IProjectDataService {

    /**
     * 根据项目id查询项目信息
     */
    ProjectInfoVo selectProjectInfoById(Integer projectId);

    /**
     * 查询页面字段列表
     */
    List<ProjectPageFieldVo> selectPageFieldList(ProjectPageFieldBo bo);

    /**
     * 查询字段选项列表
     */
    List<ProjectFieldSelectionVo> selectFieldSelctionList(ProjectFieldSelectionBo bo);

    /**
     * 查询工作流状态列表
     */
    List<WorkflowStateVo> selectStateList(WorkflowStateBo bo);

    /**
     * 查询工作流流转列表
     */
    List<WorkflowTransitionVo> selectTransitionList(WorkflowTransitionBo bo);

    /**
     * 查询项目成员列表
     */
    List<ProjectMemberVo> selectMemberList(Integer projectId);

    /**
     * 查询系统用户列表
     */
    List<SysUserSimpleInfoVo> selectSysUserList();

    /**
     * 根据部门ID查询部门名称
     */
    String getDeptNameById(Long deptId);

    /**
     * 根据项目ID查询项目类型
     */
    List<ProjectItemTypeVo> selectItemTypeList(Integer projectId);
}
