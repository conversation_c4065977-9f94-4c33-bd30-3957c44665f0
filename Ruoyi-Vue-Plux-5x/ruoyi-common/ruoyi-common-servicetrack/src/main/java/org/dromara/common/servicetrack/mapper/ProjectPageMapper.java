package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ProjectPage;
import org.dromara.common.servicetrack.domain.bo.ProjectPageBo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageVo;

public interface ProjectPageMapper extends BaseMapperPlus<ProjectPage, ProjectPageVo> {
    default LambdaQueryWrapper<ProjectPage> buildWrapper(ProjectPageBo bo) {
        LambdaQueryWrapper<ProjectPage> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProjectId() != null, ProjectPage::getProjectId, bo.getProjectId());
        lqw.eq(bo.getPageId() != null, ProjectPage::getPageId, bo.getPageId());
        lqw.like(StringUtils.isNotBlank(bo.getPageName()), ProjectPage::getPageName, bo.getPageName());
        lqw.eq(bo.getModuleId() != null, ProjectPage::getModuleId, bo.getModuleId());
        return lqw;
    }
}