package org.dromara.common.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.ProjectMember;
import org.dromara.common.servicetrack.domain.bo.ProjectMemberBinderBo;
import org.dromara.common.servicetrack.domain.bo.ProjectMemberBo;
import org.dromara.common.servicetrack.domain.vo.ProjectMemberVo;
import org.dromara.common.servicetrack.domain.vo.SysUserSimpleInfoVo;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.mapper.ProjectMemberMapper;
import org.dromara.common.servicetrack.service.IProjectMemberService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 项目成员管理 服务层实现
 *
 * <AUTHOR> Fei
 */
@RequiredArgsConstructor
@Service
public class ProjectMemberServiceImpl implements IProjectMemberService {
    private final ProjectMemberMapper baseMapper;
    @Override
    public List<ProjectMemberVo> selectMemberList(Integer projectId) {
        return baseMapper.selectMemberList(projectId);
    }

    @Override
    public List<SysUserSimpleInfoVo> selectUserList() {
        return baseMapper.selectUserList();
    }

    @Override
    public TableDataInfo<ProjectMemberVo> queryPageList(Integer projectId, PageQuery pageQuery) {
        ProjectMemberBo bo = new ProjectMemberBo();
        bo.setProjectId(projectId);
        QueryWrapper<ProjectMemberBo> wrapper = Wrappers.query();
        wrapper.eq(bo.getProjectId() != null, "project_id", bo.getProjectId());

        Page<ProjectMemberVo> page = baseMapper.selectMemberListByPage(pageQuery.build(), projectId);
        return TableDataInfo.build(page);
    }

    @Override
    public Boolean insertByBo(ProjectMemberBinderBo bo) {
        return updateProjectMember(bo);
    }

    @Override
    public Boolean updateByBo(ProjectMemberBinderBo bo) {
        return updateProjectMember(bo);
    }
    private  boolean updateProjectMember(ProjectMemberBinderBo bo){
        if( bo.getProjectMembers() == null || bo.getProjectMembers().isEmpty()){
            return false;
        }
        Integer projectId = bo.getProjectId();
        var existedMembers =  baseMapper.selectMemberList(projectId);;

        List<ProjectMember> insertProjectMembers = new ArrayList<>();
        List<ProjectMember> updateProjectMembers = new ArrayList<>();
        for(var member:bo.getProjectMembers()){
            boolean isExisted = false;
            ProjectMemberVo existedMember = null;
            if( !existedMembers.isEmpty() )
            {
                existedMember = existedMembers.stream()
                    .filter(m -> m.getUserId().equals(member.getUserId()) && m.getUserType().equals(member.getUserType()))
                    .findFirst()
                    .orElse(null);
            }
            if( existedMember == null){
                ProjectMemberBo insertBo = new ProjectMemberBo();
                insertBo.setProjectId(projectId);
                insertBo.setUserId(member.getUserId());
                insertBo.setUserType(member.getUserType());
                insertBo.setAccountTypeId(member.getAccountTypeId());
                ProjectMember projectMember = MapstructUtils.convert(insertBo, ProjectMember.class);
                insertProjectMembers.add(projectMember);
            }
            else {
                boolean isExistedAccount =  existedMembers.stream().allMatch(m->m.getAccountTypeId().equals(member.getAccountTypeId()));
                if( !isExistedAccount){

                    ProjectMemberBo updateBo = new ProjectMemberBo();
                    updateBo.setId(existedMember.getId());
                    updateBo.setProjectId(projectId);
                    updateBo.setUserId(member.getUserId());
                    updateBo.setUserType(member.getUserType());
                    updateBo.setAccountTypeId(member.getAccountTypeId());
                    ProjectMember projectMember = MapstructUtils.convert(updateBo, ProjectMember.class);
                    updateProjectMembers.add(projectMember);

                }
            }
        }
        boolean changed = false;
        if( !insertProjectMembers.isEmpty()){
            baseMapper.insertBatch(insertProjectMembers);
            changed = true;
        }
        if( !updateProjectMembers.isEmpty()){
            baseMapper.updateBatchById(updateProjectMembers);
            changed = true;
        }


        // Update the project members in the ProjectManager instance
        if( changed ) {
            var projectMembersList = baseMapper.selectMemberList(projectId);
            ProjectManager.getInstance(projectId).setProjectMembers(projectMembersList);
        }
        return true;
    }
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        var projectId = baseMapper.selectById(ids.stream().findFirst().get()).getProjectId();
        if( projectId == null){
            throw new ServiceException("can't get project Id.");
        }

        // Update the project members in the ProjectManager instance
        var projectMembersList = baseMapper.selectMemberList(projectId);
        ProjectManager.getInstance(projectId).setProjectMembers(projectMembersList);
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public String getDeptNameById(Long deptId) {
        return baseMapper.getDeptNameById(deptId);
    }
}
