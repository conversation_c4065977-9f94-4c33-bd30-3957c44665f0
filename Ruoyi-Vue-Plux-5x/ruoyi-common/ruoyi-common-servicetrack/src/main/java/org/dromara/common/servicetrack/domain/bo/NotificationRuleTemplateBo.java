package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.NotificationRuleTemplate;

/**
 * 通知规则模板业务对象 notification_rule_template
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = NotificationRuleTemplate.class)
public class NotificationRuleTemplateBo extends STBaseEntity {
    /**
     * ID
     */
    private Long keyId;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 规则ID
     */
    @NotNull(message = "规则ID不能为空")
    private Integer ruleId;

    /**
     * 模板类型
     */
    @NotNull(message = "模板类型不能为空")
    private Integer templateType;

    /**
     * 模板ID
     */
    @NotNull(message = "模板ID不能为空")
    private Integer templateId;
}
