package org.dromara.common.servicetrack.constant;

public enum eSystemPageDef implements IValueEnum {
    History(1),
    SLA(2),
    Activity(3),
    Assets(4),
    AllLinks(5),
    Attachments(6),

    LastMax(499);
    private final int value;

    /*
     *构造函数在定义常量时自动调用
     */
    eSystemPageDef(int value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return value;
    }

    public String getName() {
        return switch (this.value) {
            case 1 -> "历史记录";//"History";
            case 2 -> "SLA详情";
            case 3 -> "所有活动";//"Activity";
            case 4 -> "资产";//"Assets";
            case 5 -> "链接";//"AllLinks";
            case 6 -> "附件";//"Attachments";
            default -> "";
        };

    }
    public static eSystemPageDef from(Integer value) {
        return IValueEnum.valueOf(eSystemPageDef.class, value);
    }
}
