package org.dromara.common.servicetrack.utils;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.sql.Timestamp;

/**
 * Utility class for value conversion and validation
 */
public final class ValueConvert {


    private ValueConvert() {
    }

    /**
     * Validate the input data of String type.
     */
    @SuppressWarnings("unchecked")
    public static String readString(Map<String, Object> row, String column) {
        return readString(row, column, "");
    }

    /**
     * Validate the input data of String type.
     */
    @SuppressWarnings("unchecked")
    public static String readString(Map<String, Object> row, String column, String defaultValue) {
        Object data = row.get(column);
        if (data instanceof Map) {
            return readString((Map<String, Object>) data, defaultValue);
        } else {
            // 处理 data 不是 Map 的情况
            return readString(data, defaultValue,false);
        }
    }

    /**
     * Validate the input data of String type with column name.
     */
    @SuppressWarnings("unchecked")
    public static String readString(Map<String, Object> row, String column, boolean encode) {
        return readString(row, column, "", encode);
    }

    /**
     * Validate the input data of String type with column name.
     */
    @SuppressWarnings("unchecked")
    public static String readString(Map<String, Object> row, String column, String defaultValue, boolean encode) {
        Object data = row.get(column);
        return readString(data, defaultValue, encode);
    }

    /**
     * Validate the input data of long type.
     */
    public static long readLong(Map<String, Object> row, String column) {
        return readLong(row, column, 0L);
    }

    /**
     * Validate the input data of long type.
     */
    public static long readLong(Map<String, Object> row, String column, long defaultValue) {
        Object data = row.get(column);
        return readLong(data, defaultValue);
    }

    /**
     * Validate the input data of int type.
     */
    public static int readInt(Map<String, Object> row, String column) {
        return readInt(row, column, 0);
    }

    /**
     * Validate the input data of int type.
     */
    public static int readInt(Map<String, Object> row, String column, int defaultValue) {
        Object data = row.get(column);
        return readInt(data, defaultValue);
    }

    /**
     * Validate the input data of short type.
     */
    public static short readShort(Map<String, Object> row, String column) {
        return readShort(row, column, (short)0);
    }

    /**
     * Validate the input data of short type.
     */
    public static short readShort(Map<String, Object> row, String column, short defaultValue) {
        Object data = row.get(column);
        return readShort(data, defaultValue);
    }

    /**
     * Validate the input data of double type.
     */
    public static double readDouble(Map<String, Object> row, String column) {
        return readDouble(row, column, 0.0);
    }

    /**
     * Validate the input data of double type.
     */
    public static double readDouble(Map<String, Object> row, String column, double defaultValue) {
        Object data = row.get(column);
        return readDouble(data, defaultValue);
    }

    /**
     * Validate the input data of float type.
     */
    public static float readFloat(Map<String, Object> row, String column) {
        return readFloat(row, column, 0.0f);
    }

    /**
     * Validate the input data of float type.
     */
    public static float readFloat(Map<String, Object> row, String column, float defaultValue) {
        Object data = row.get(column);
        return readFloat(data, defaultValue);
    }

    /**
     * Validate the input data of Date type.
     */
    public static Date readDateTime(Map<String, Object> row, String column) {
        Object data = row.get(column);
        return readDateTime(data);
    }

    /**
     * Validate the input data of boolean type.
     */
    public static boolean readBool(Map<String, Object> row, String column) {
        return readBool(row, column, false);
    }

    /**
     * Validate the input data of boolean type.
     */
    public static boolean readBool(Map<String, Object> row, String column, boolean defaultValue) {
        Object data = row.get(column);
        return readBool(data, defaultValue);
    }

    // Base type conversion methods
    /**
     * Validate the input data of String type.
     */
    public static String readString(Object data) {
        return readString(data, "", false);
    }

    /**
     * Validate the input data of String type.
     */
    public static String readString(Object data, String defaultValue, boolean encode) {
        if (data == null) {
            return defaultValue;
        }

        String text = data.toString();
        if (encode) {
            text = escapeHtml(text);
        }

        return text;
    }

    /**
     * Validate the input data of long type.
     */
    public static long readLong(Object data) {
        return readLong(data, 0L);
    }

    /**
     * Validate the input data of long type.
     */
    public static long readLong(Object data, long defaultValue) {
        if (data == null) {
            return defaultValue;
        }

        try {
            if (data instanceof Long) {
                return (Long) data;
            } else if (data instanceof Integer) {
                return ((Integer) data).longValue();
            } else if (data instanceof Short) {
                return ((Short) data).longValue();
            } else if (data instanceof java.math.BigDecimal) {
                return ((java.math.BigDecimal) data).longValue();
            } else {
                return Long.parseLong(data.toString());
            }
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * Validate the input data of int type.
     */
    public static int readInt(Object data) {
        return readInt(data, 0);
    }
    /**
     * Validate the input data of List<Integer> type.
     */
    public static List<Integer> readListInt(Object data) {
        if (data == null) {
            return new ArrayList<>();
        }

        if (data instanceof List<?> dataList) {
            List<Integer> result = new ArrayList<>(dataList.size());
            for (Object item : dataList) {
                if (item instanceof Integer) {
                    result.add((Integer) item);
                } else if (item != null) {
                    try {
                        result.add(Integer.parseInt(item.toString()));
                    } catch (NumberFormatException e) {
                        // Ignore invalid conversion and continue
                    }
                }
            }
            return result;
        } else {
            throw new IllegalArgumentException("Input data must be a List<Integer>");
        }
    }
    /**
     * Validate the input data of int type.
     */
    public static int readInt(Object data, int defaultValue) {
        if (data == null) {
            return defaultValue;
        }

        try {
            if (data instanceof Integer) {
                return (Integer) data;
            } else if (data instanceof Short) {
                return ((Short) data).intValue();
            } else if (data instanceof Long) {
                return ((Long) data).intValue();
            } else if (data instanceof Float) {
                return ((Float) data).intValue();
            } else if (data instanceof Double) {
                return ((Double) data).intValue();
            } else if (data instanceof java.math.BigDecimal) {
                return ((java.math.BigDecimal) data).intValue();
            } else {
                return Integer.parseInt(data.toString());
            }
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * Validate the input data of short type.
     */
    public static short readShort(Object data) {
        return readShort(data, (short)0);
    }

    /**
     * Validate the input data of short type.
     */
    public static short readShort(Object data, short defaultValue) {
        if (data == null) {
            return defaultValue;
        }

        try {
            if (data instanceof Short) {
                return (Short) data;
            } else if (data instanceof java.math.BigDecimal) {
                return ((java.math.BigDecimal) data).shortValue();
            } else {
                return Short.parseShort(data.toString());
            }
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * Validate the input data of double type.
     */
    public static double readDouble(Object data) {
        return readDouble(data, 0.0);
    }

    /**
     * Validate the input data of double type.
     */
    public static double readDouble(Object data, double defaultValue) {
        if (data == null) {
            return defaultValue;
        }

        try {
            if (data instanceof Double) {
                return (Double) data;
            } else if (data instanceof Float) {
                return ((Float) data).doubleValue();
            } else if (data instanceof java.math.BigDecimal) {
                return ((java.math.BigDecimal) data).doubleValue();
            } else {
                return Double.parseDouble(data.toString());
            }
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * Validate the input data of float type.
     */
    public static float readFloat(Object data) {
        return readFloat(data, 0.0f);
    }

    /**
     * Validate the input data of float type.
     */
    public static float readFloat(Object data, float defaultValue) {
        if (data == null) {
            return defaultValue;
        }

        try {
            if (data instanceof Float) {
                return (Float) data;
            } else if (data instanceof Double) {
                return ((Double) data).floatValue();
            } else if (data instanceof java.math.BigDecimal) {
                return ((java.math.BigDecimal) data).floatValue();
            } else {
                return Float.parseFloat(data.toString());
            }
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * Validate the input data of LocalDateTime type.
     */
//    public static Date readDateTime(Object data) {
//        return readDateTime(data);
//    }

    /**
     * Validate the input data of Date type.
     */
    public static Date readDateTime(Object data) {
        if (data == null) {
            return new Date(0);
        }

        try {
            if (data instanceof Date) {
                return (Date) data;
            }
//            else if (data instanceof Timestamp) {
//                return ((Timestamp) data).toLocalDateTime();
//            }
            else {
                return new Date(0);
            }
        } catch (Exception e) {
            return new Date(0);
        }
    }

    /**
     * Validate the input data of Optional LocalDateTime type.
     */
    public static Date readDateTime2(Object data) {
        if (data == null) {
            return null;
        }

        try {
            if (data instanceof Date) {
                return (Date) data;
            }
//            else if (data instanceof Timestamp) {
//                return ((Timestamp) data).toLocalDateTime();
//            }
        } catch (Exception e) {
            // ignore
        }
        return null;
    }

    /**
     * Validate the input data of boolean type.
     */
    public static boolean readBool(Object data) {
        return readBool(data, false);
    }

    /**
     * Validate the input data of boolean type.
     */
    public static boolean readBool(Object data, boolean defaultValue) {
        if (data == null) {
            return defaultValue;
        }

        try {
            if (data instanceof Boolean) {
                return (Boolean) data;
            } else {
                int tmp = readInt(data, 0);
                return tmp != 0;
            }
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * Check if data is null
     */
    public static boolean isNull(Object data) {
        return data == null;
    }

    /**
     * Decode HTML encoded string
     */
    public static String decodeString(String text) {
        if (text == null) {
            return null;
        }
        return text.replace("&lt;", "<")
            .replace("&gt;", ">")
            .replace("&amp;", "&")
            .replace("&quot;", "\"")
            .replace("&#39;", "'");
    }
    public static String decodeBase64(Object value) {
        if (value == null) {
            return null;
        }
        String strValue = ValueConvert.readString(value); // 获取字符串值
        if (strValue == null || strValue.isEmpty()) {
            return null;
        }


        byte[] decodedBytes = Base64.getDecoder().decode(strValue); // Base64解码
        return new String(decodedBytes, StandardCharsets.UTF_8); // 将字节数组转换为字符
    }
    public static String encodeBase64(Object value) {
        if (value == null ||  value.toString().isEmpty()) {
            return null;
        }
        String strValue = ValueConvert.readString(value); // 获取字符串值
        if (strValue == null || strValue.isEmpty()) {
            return null;
        }
        byte[] bytes = strValue.getBytes(StandardCharsets.UTF_8); // UTF-8编码转字节
        return Base64.getEncoder().encodeToString(bytes); // Base64编码
    }

    // Helper method for HTML escaping
    private static String escapeHtml(String input) {
        if (input == null) {
            return null;
        }
        StringBuilder escaped = new StringBuilder();
        for (char c : input.toCharArray()) {
            switch (c) {
                case '<':
                    escaped.append("&lt;");
                    break;
                case '>':
                    escaped.append("&gt;");
                    break;
                case '&':
                    escaped.append("&amp;");
                    break;
                case '"':
                    escaped.append("&quot;");
                    break;
                case '\'':
                    escaped.append("&#39;");
                    break;
                default:
                    escaped.append(c);
            }
        }
        return escaped.toString();
    }
    @SuppressWarnings("unchecked")
    public static List<Integer> safeCastToList(Object obj) {
        if (obj instanceof List<?> list) {
            if (list.isEmpty() || list.get(0) instanceof Integer) {
                return (List<Integer>) list;
            }
        }
        return Collections.emptyList();
    }
}
