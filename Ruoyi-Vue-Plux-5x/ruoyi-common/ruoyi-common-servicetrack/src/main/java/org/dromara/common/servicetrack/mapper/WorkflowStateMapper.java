package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;

import org.dromara.common.servicetrack.domain.WorkflowState;
import org.dromara.common.servicetrack.domain.bo.WorkflowStateBo;
import org.dromara.common.servicetrack.domain.vo.WorkflowStateVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 工作流状态 数据层
 *
 * <AUTHOR> fei
 */
public interface WorkflowStateMapper extends  BaseMapperPlus<WorkflowState, WorkflowStateVo> {
    default LambdaQueryWrapper<WorkflowState> buildWrapper(WorkflowStateBo bo) {
        LambdaQueryWrapper<WorkflowState> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, WorkflowState::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, WorkflowState::getProjectId, bo.getProjectId());
        lqw.eq(bo.getStateId() != null , WorkflowState::getStateId, bo.getStateId());
        lqw.like(StringUtils.isNotBlank(bo.getStateName()), WorkflowState::getStateName, bo.getStateName());

        return lqw;
    }
}
