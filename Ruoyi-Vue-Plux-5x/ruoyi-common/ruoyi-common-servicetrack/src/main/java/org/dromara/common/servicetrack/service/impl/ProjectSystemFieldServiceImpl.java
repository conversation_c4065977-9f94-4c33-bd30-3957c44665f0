package org.dromara.common.servicetrack.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;


import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.servicetrack.constant.eSTModuleIDDef;
import org.dromara.common.servicetrack.domain.ProjectFieldCustom;
import org.dromara.common.servicetrack.domain.ProjectSystemField;
import org.dromara.common.servicetrack.domain.bo.ProjectSystemFieldBo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldVo;
import org.dromara.common.servicetrack.logic.helper.FieldIdHelper;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.mapper.ProjectSystemFieldMapper;
import org.dromara.common.servicetrack.service.IProjectSystemFieldService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 字段管理 服务层实现
 *
 * <AUTHOR> <PERSON>
 */
@RequiredArgsConstructor
@Service
public class ProjectSystemFieldServiceImpl implements IProjectSystemFieldService {
    private final ProjectSystemFieldMapper projectSystemFieldMapper;

    /**
     * 根据条件查询所有字段列表
     *
     * @param field 字段信息
     * @return 字段信息集合信息
     */
    @Override
    public List<ProjectFieldVo> selectFieldList(ProjectSystemFieldBo field) {
        QueryWrapper<ProjectSystemField> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq(ObjectUtil.isNotNull(field.getFieldId()), "field_id", field.getFieldId())
            .eq(ObjectUtil.isNotNull(field.getProjectId()), "project_id",field.getProjectId())
            .eq(ObjectUtil.isNotNull(field.getModuleId()), "module_id", field.getModuleId());


        var fieldList = projectSystemFieldMapper.selectFieldList(queryWrapper);
        //check if missing some system fields, if so, add them
        Integer projectId = field.getProjectId();

        Boolean isBaseProject = ProjectManager.getInstance(projectId).isBaseProject();
        if(field.getModuleId() == null ||  field.getModuleId() == 0){
            if( isBaseProject ){
                field.setModuleId(eSTModuleIDDef.UserInfo.getValue());
            }
            else{
                field.setModuleId(eSTModuleIDDef.Incident.getValue());
            }
        }

        List<ProjectSystemField> missingSystemFields = new ArrayList<>();
        var systemFields = isBaseProject ? FieldIdHelper.AllUserInfoSystemFields: FieldIdHelper.AllModuleSystemFields;
        for (var systemFieldId : systemFields) {
            int fieldId = systemFieldId.getValue();
            if( fieldId == 0)
                continue;
            boolean found = false;
            for (int j = 0; j < fieldList.size(); j++) {
                ProjectFieldVo field1 = fieldList.get(j);
                if (field1.getFieldId() == fieldId) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                ProjectSystemFieldBo bo1 = new ProjectSystemFieldBo();
                bo1.setProjectId(projectId);
                bo1.setFieldId(fieldId);
                bo1.setFieldType(systemFieldId.getFieldType());
                bo1.setFieldName(systemFieldId.getName());
                bo1.setFieldDefaultName(systemFieldId.getDefaultName());
                bo1.setModuleId(field.getModuleId());
                ProjectSystemField sysField = MapstructUtils.convert(bo1, ProjectSystemField.class);
                missingSystemFields.add(sysField);
            }
        }
        if (!missingSystemFields.isEmpty()) {
            projectSystemFieldMapper.insertBatch(missingSystemFields);

            fieldList = projectSystemFieldMapper.selectFieldList(queryWrapper);
        }
        return fieldList;
    }

    @Override
    public Integer updateByBo(ProjectSystemFieldBo bo) {
        ProjectSystemField update = MapstructUtils.convert(bo, ProjectSystemField.class);
        return projectSystemFieldMapper.updateById(update) > 0 ? bo.getFieldId() : 0;
    }
}
