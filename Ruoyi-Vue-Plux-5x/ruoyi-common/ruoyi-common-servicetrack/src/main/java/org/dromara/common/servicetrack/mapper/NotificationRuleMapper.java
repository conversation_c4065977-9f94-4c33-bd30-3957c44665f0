package org.dromara.common.servicetrack.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ItemAttachment;
import org.dromara.common.servicetrack.domain.NotificationRule;
import org.dromara.common.servicetrack.domain.bo.NotificationRuleBo;
import org.dromara.common.servicetrack.domain.vo.ItemAttachmentVo;
import org.dromara.common.servicetrack.domain.vo.NotificationRuleVo;

/**
 * notification_rule表的mapper接口
 */
public interface NotificationRuleMapper extends BaseMapperPlus<NotificationRule, NotificationRuleVo> {
    /**
     * 查询notification_rule表的所有数据
     *
     * @return 返回notification_rule表的所有数据
     */
    List<NotificationRuleVo> selectAll();

    /**
     * 根据主键查询notification_rule表的数据
     *
     * @param projectId 主键值
     * @param ruleId    主键值
     * @return 返回主键对应的数据
     */
    NotificationRuleVo selectByPk(@Param("projectId") Integer projectId, @Param("ruleId") Integer ruleId);

    /**
     * 插入数据到notification_rule表
     *
     * @param bo 需要插入的数据
     * @return 返回插入数据影响的行数
     */
    int insertByBo(NotificationRuleBo bo);

    /**
     * 更新notification_rule表中的数据
     *
     * @param bo 需要更新的数据
     * @return 返回更新数据影响的行数
     */
    int updateByBo(NotificationRuleBo bo);
}
