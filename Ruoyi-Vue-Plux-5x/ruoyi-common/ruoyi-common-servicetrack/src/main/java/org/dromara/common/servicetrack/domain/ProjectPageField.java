package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *  project_page_field
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_page_field")
public class ProjectPageField extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * page id
     */
    @TableField(value = "page_id")
    private Integer pageId;

    /**
     * field id
     */
    @TableField(value = "field_id")
    private Integer fieldId;

    /**
     * page row
     */
    @TableField(value = "page_row")
    private Integer pageRow;

    /**
     * page column
     */
    @TableField(value = "page_column")
    private Integer pageColumn;

}
