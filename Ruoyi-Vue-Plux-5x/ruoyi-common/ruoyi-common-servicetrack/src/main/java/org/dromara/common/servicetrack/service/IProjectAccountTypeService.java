package org.dromara.common.servicetrack.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectAccountTypeBo;
import org.dromara.common.servicetrack.domain.bo.ProjectInfoBo;
import org.dromara.common.servicetrack.domain.vo.ProjectAccountTypeVo;
import org.dromara.common.servicetrack.domain.vo.ProjectInfoVo;

import java.util.Collection;
import java.util.List;

public interface IProjectAccountTypeService {
    /**
     * 查询项目账户
     */
    ProjectAccountTypeVo queryById(Long id);

    ProjectAccountTypeVo selectProjectAccountTypeById(Integer projectId,Integer accountTypeId);
    /**
     * 查询项目账户列表
     */
    TableDataInfo<ProjectAccountTypeVo> queryPageList(Integer projectId,PageQuery pageQuery);

    List<ProjectAccountTypeVo> queryList(ProjectAccountTypeBo bo);
    /**
     * 新增项目账户
     */
    Integer insertByBo(ProjectAccountTypeBo bo);

    /**
     * 修改项目账户
     */
    Integer updateByBo(ProjectAccountTypeBo bo);

    /**
     * 校验并批量删除项目账户信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
