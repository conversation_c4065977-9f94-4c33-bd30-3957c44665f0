package org.dromara.common.servicetrack.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.servicetrack.domain.ProjectPageAction;
import org.dromara.common.servicetrack.domain.ProjectPageField;
import org.dromara.common.servicetrack.domain.ProjectSystemField;
import org.dromara.common.servicetrack.domain.bo.*;
import org.dromara.common.servicetrack.domain.vo.ProjectPageFieldVo;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.mapper.ProjectPageFieldMapper;
import org.dromara.common.servicetrack.mapper.ProjectSystemFieldMapper;
import org.dromara.common.servicetrack.service.IProjectPageFieldServiceEx;
import org.springframework.stereotype.Service;
import org.dromara.common.servicetrack.constant.eUserSystemFieldDef;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 字段管理 服务层实现
 *
 * <AUTHOR> Fei
 */
@RequiredArgsConstructor
@Service
public class ProjectPageFieldServiceExImpl implements IProjectPageFieldServiceEx {
    private final ProjectPageFieldMapper projectPageFieldMapper;
    private final ProjectSystemFieldMapper projectSystemFieldMapper;

    /**
     * 根据条件查询页面字段列表
     *
     * @param field 字段信息
     * @return 字段信息集合信息
     */
    @Override
    public List<ProjectPageFieldVo> selectPageFieldList(ProjectPageFieldBo field,boolean fromCache) {
        if(fromCache)
        {
            return ProjectManager.getInstance(field.getProjectId()).getProjectPageFields();
        }
        QueryWrapper<ProjectPageField> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq(ObjectUtil.isNotNull(field.getPageId()), "f.page_id", field.getPageId())
            .eq(ObjectUtil.isNotNull(field.getProjectId()), "f.project_id", field.getProjectId());

        var list = projectPageFieldMapper.selectPageFieldList(queryWrapper);

        list = list.stream().sorted(Comparator.comparing(ProjectPageFieldVo::getPageId)).collect(Collectors.toList());

        //get all fields data
        QueryWrapper<ProjectSystemField> fieldqueryWrapper = new QueryWrapper<>();
        ProjectSystemFieldBo sysFieldBo = new ProjectSystemFieldBo();
        sysFieldBo.setProjectId(field.getProjectId());
        sysFieldBo.setFieldId(field.getFieldId());
        fieldqueryWrapper.eq(ObjectUtil.isNotNull(field.getFieldId()), "field_id", sysFieldBo.getFieldId())
            .eq(ObjectUtil.isNotNull(field.getProjectId()), "project_id",sysFieldBo.getProjectId());
        var fieldList = projectSystemFieldMapper.selectFieldList(fieldqueryWrapper);
        for (ProjectPageFieldVo pageFieldVo : list) {

            var matchedSysFieldVo = fieldList.stream().filter(sysFieldVo -> sysFieldVo.getFieldId().equals(pageFieldVo.getFieldId())).findFirst();
            if( matchedSysFieldVo.isPresent())
            {
                pageFieldVo.setFieldName(matchedSysFieldVo.get().getFieldName());
                pageFieldVo.setFieldType(matchedSysFieldVo.get().getFieldType());
            }
        }
        return list;
    }

    @Override
    public List<ProjectPageFieldVo> selectPageFieldList(Integer projectId,Integer pageId) {
        QueryWrapper<ProjectPageField> queryWrapper = new QueryWrapper<>();

        ProjectPageFieldBo field = new ProjectPageFieldBo();
        field.setProjectId(projectId);
        field.setPageId(pageId);
        var list = projectPageFieldMapper.selectVoList(this.buildQueryWrapper(field));
        if( !list.isEmpty()){
            var pageFields = ProjectManager.getInstance(field.getProjectId()).getProjectPageFields();
            for (ProjectPageFieldVo pageFieldVo : list) {
                var matchedSysFieldVo = pageFields.stream().filter(sysFieldVo -> sysFieldVo.getPageId().equals(pageFieldVo.getPageId()) && sysFieldVo.getFieldId().equals(pageFieldVo.getFieldId())).findFirst();
                if( matchedSysFieldVo.isPresent())
                {
                    pageFieldVo.setFieldName(matchedSysFieldVo.get().getFieldName());
                    pageFieldVo.setFieldType(matchedSysFieldVo.get().getFieldType());
                }
            }
            boolean isBaseProject = ProjectManager.getInstance(projectId).isBaseProject();
            //if it is base project, then remove supportTeam/primarySupport/secondarySupport fields
            if( isBaseProject){
                list.removeIf(pageFieldVo -> pageFieldVo.getFieldId() ==  eUserSystemFieldDef.SupportTeam.getValue() ||
                    pageFieldVo.getFieldId() == eUserSystemFieldDef.PrimarySupport.getValue() || pageFieldVo.getFieldId() == eUserSystemFieldDef.SecondarySupport.getValue());
            }
            //sort by page row and page column
            list.sort((a, b) -> {
                int rowCompare = Integer.compare(a.getPageRow(), b.getPageRow());
                return rowCompare != 0 ? rowCompare : Integer.compare(a.getPageColumn(), b.getPageColumn());
            });
        }

        return list;
    }
    private Wrapper<ProjectPageField> buildQueryWrapper(ProjectPageFieldBo bo) {
        QueryWrapper<ProjectPageField> wrapper = Wrappers.query();
        wrapper.eq(bo.getProjectId() != null, "project_id", bo.getProjectId());
        wrapper.eq(bo.getId() != null, "key_id", bo.getId());
        wrapper.eq(bo.getPageId() != null, "page_id", bo.getPageId());
        wrapper.eq(bo.getFieldId() != null, "field_id", bo.getFieldId());
        wrapper.eq(bo.getPageRow() != null, "page_row", bo.getPageRow())
                .eq(bo.getPageColumn() != null, "page_column", bo.getPageColumn());
        return wrapper;
    }
    private Integer GetMaxRowNum(ProjectPageFieldBo bo){
        //get the max row using MAX function
        QueryWrapper<ProjectPageField> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("COALESCE(MAX(page_row), 0) as page_row")
            .eq(ObjectUtil.isNotNull(bo.getPageId()), "page_id", bo.getPageId())
            .eq(ObjectUtil.isNotNull(bo.getProjectId()), "project_id", bo.getProjectId());

        return projectPageFieldMapper.selectOne(queryWrapper).getPageRow();
    }
    @Override
    public Boolean insertByBo(ProjectPageFieldBo bo) {
        Integer projectId = bo.getProjectId();

        //check if the row which is larger than 0 is existed,
        if( bo.getPageRow() > 0){
            ProjectPageFieldBo rowBo = new ProjectPageFieldBo();
            rowBo.setProjectId(bo.getProjectId());
            rowBo.setPageId(bo.getPageId());
            rowBo.setPageRow(bo.getPageRow());
            var matchedRows = projectPageFieldMapper.selectVoList(this.buildQueryWrapper(rowBo));
            if( matchedRows != null && !matchedRows.isEmpty() ){
                //check which position is the input column under current row
                if (bo.getPageColumn() <= 0) {
                    // If no specific column is provided, append to the end
                    bo.setPageColumn(matchedRows.size() + 1);
                } else {
                    //Shift existing columns to make space for the new column
                    List<ProjectPageField> updateFields = new ArrayList<>();
                    for (ProjectPageFieldVo existingField : matchedRows) {
                        if (existingField.getPageColumn() >= bo.getPageColumn()) {
                            ProjectPageField updateField = new ProjectPageField();
                            updateField.setId(existingField.getId());
                            updateField.setPageColumn(existingField.getPageColumn() + 1);
                            updateFields.add(updateField);
                        }
                    }
                    if (!updateFields.isEmpty()) {
                        projectPageFieldMapper.updateBatchById(updateFields);
                    }
                }
            }
            else{
                //get the max row using MAX function
                Integer maxRow = GetMaxRowNum(bo);
                bo.setPageRow(maxRow + 1);
                bo.setPageColumn(1);
            }
        }
        else{
            //get the max row using MAX function
            Integer maxRow = GetMaxRowNum(bo);
            bo.setPageRow(maxRow + 1);
            bo.setPageColumn(1);
        }

        var ProjectPageField = MapstructUtils.convert(bo, ProjectPageField.class);
        projectPageFieldMapper.insert(ProjectPageField);
        //reset cache
        ProjectManager.getInstance(projectId).ResetProjectPageFields();
        return true;
    }

    @Override
    public Boolean updateByBo(ProjectPageFieldBinderBo bo) {
        Integer projectId = bo.getProjectId();
        Integer pageId = bo.getPageId();
        if( bo.getPageFields() == null || bo.getPageFields().isEmpty())
            return false;
        //first delete all row and column data for this page
        projectPageFieldMapper.delete(new QueryWrapper<ProjectPageField>().eq("project_id",projectId).eq("page_id",pageId));

        List<ProjectPageField> fieldlist = new ArrayList<>();
        for (var pageField : bo.getPageFields()) {
            ProjectPageFieldBo fieldBo = new ProjectPageFieldBo();
            fieldBo.setProjectId(projectId);
            fieldBo.setPageId(pageId);
            fieldBo.setFieldId(pageField.getFieldId());
            fieldBo.setPageRow(pageField.getPageRow());
            fieldBo.setPageColumn(pageField.getPageColumn());
            ProjectPageField pageFieldEntity = MapstructUtils.convert(fieldBo, ProjectPageField.class);
            fieldlist.add(pageFieldEntity);
        }
        if( !fieldlist.isEmpty()){
            projectPageFieldMapper.insertBatch(fieldlist);
        }
        //reset cache
        ProjectManager.getInstance(projectId).ResetProjectPageFields();
        return  true;
    }

    @Override
    public Boolean moveRowByBo(ProjectPageFieldMoveRowBo bo) {
        Integer projectId = bo.getProjectId();
        //reset cache
        ProjectManager.getInstance(projectId).ResetProjectPageFields();
        return true;
    }

    @Override
    public Boolean moveColumnByBo(ProjectPageFieldBinderBo bo) {
//        Integer projectId = bo.getProjectId();
//        //reset cache
//        ProjectManager.getInstance(projectId).ResetProjectPageFields();
        return true;
    }

    @Override
    public Boolean deleteWithValidByIds(Integer projectId, Collection<Long> keyIds, Boolean isValid) {
        boolean result =  projectPageFieldMapper.deleteByIds(keyIds) > 0;

        //reset cache
        ProjectManager.getInstance(projectId).ResetProjectPageFields();
        return result;
    }
}
