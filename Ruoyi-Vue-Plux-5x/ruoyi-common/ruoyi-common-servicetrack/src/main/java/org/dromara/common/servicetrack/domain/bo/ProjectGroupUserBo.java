package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ProjectGroupUser;

import java.util.List;

/**
 * 项目分组用户业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectGroupUser.class)
public class ProjectGroupUserBo extends STBaseEntity {
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 分组ID
     */
    @NotNull(message = "分组ID不能为空")
    private Integer groupId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户Ids
     */
    private List<Integer> userIds;
}
