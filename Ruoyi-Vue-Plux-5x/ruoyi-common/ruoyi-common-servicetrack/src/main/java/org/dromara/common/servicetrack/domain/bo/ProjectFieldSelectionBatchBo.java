package org.dromara.common.servicetrack.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.List;

/**
 * 批量项目字段选择项对象
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectFieldSelectionBatchBo extends STBaseEntity {

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 字段ID
     */
    @NotNull(message = "字段ID不能为空")
    private Integer fieldId;

    List<ProjectFieldSelectionInfo> selections;
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ProjectFieldSelectionInfo extends STBaseEntity {
        /**
         * 选项ID
         */
        @NotNull(message = "选项ID不能为空")
        private Integer choiceId;

        /**
         * 选项名称
         */
        @Xss(message = "选项名称不能包含脚本字符")
        @NotBlank(message = "选项名称不能为空")
        @Size(min = 0, max = 200, message = "选项名称长度不能超过{max}个字符")
        private String choiceName;
    }
}
