package org.dromara.common.servicetrack.service;

import java.util.List;

import org.dromara.common.servicetrack.domain.bo.NotificationRuleBo;
import org.dromara.common.servicetrack.domain.vo.NotificationRuleVo;

/**
 * notification_rule表的service接口
 */
public interface INotificationRuleService {
    /**
     * 查询notification_rule表的所有数据
     *
     * @return 返回notification_rule表的所有数据
     */
    List<NotificationRuleVo> selectAll();

    /**
     * 根据主键查询notification_rule表的数据
     *
     * @param projectId 主键值
     * @param ruleId    主键值
     * @return 返回主键对应的数据
     */
    NotificationRuleVo selectByPk(Integer projectId, Integer ruleId);

    /**
     * 插入数据到notification_rule表
     *
     * @param bo 需要插入的数据
     * @return 返回插入数据影响的行数
     */
    int insertByBo(NotificationRuleBo bo);

    /**
     * 更新notification_rule表中的数据
     *
     * @param bo 需要更新的数据
     * @return 返回更新数据影响的行数
     */
    int updateByBo(NotificationRuleBo bo);


}
