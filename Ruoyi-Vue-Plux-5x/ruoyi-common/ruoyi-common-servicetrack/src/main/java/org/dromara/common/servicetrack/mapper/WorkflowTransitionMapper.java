package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;

import org.dromara.common.servicetrack.domain.WorkflowTransition;
import org.dromara.common.servicetrack.domain.bo.WorkflowTransitionBo;
import org.dromara.common.servicetrack.domain.vo.WorkflowTransitionVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
/**
 * 工作流转换 数据层
 *
 * <AUTHOR> fei
 */
public interface WorkflowTransitionMapper extends BaseMapperPlus<WorkflowTransition, WorkflowTransitionVo>{
    default LambdaQueryWrapper<WorkflowTransition> buildWrapper(WorkflowTransitionBo bo) {
        LambdaQueryWrapper<WorkflowTransition> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, WorkflowTransition::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, WorkflowTransition::getProjectId, bo.getProjectId());
        lqw.eq(bo.getTransitionId() != null , WorkflowTransition::getTransitionId, bo.getTransitionId());
        lqw.like(StringUtils.isNotBlank(bo.getTransitionName()), WorkflowTransition::getTransitionName, bo.getTransitionName());

        return lqw;
    }
}
