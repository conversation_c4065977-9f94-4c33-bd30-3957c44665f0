package org.dromara.common.servicetrack.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.servicetrack.utils.ValueConvert;
import org.dromara.common.mail.config.properties.MailConfigDo;
import org.dromara.common.mail.factory.MailAccountFactory;
import org.dromara.common.servicetrack.constant.eProjectSetting;
import org.dromara.common.servicetrack.domain.bo.ProjectFieldSelectionBo;
import org.dromara.common.servicetrack.domain.bo.ProjectPageFieldBo;
import org.dromara.common.servicetrack.domain.bo.WorkflowStateBo;
import org.dromara.common.servicetrack.domain.bo.WorkflowTransitionBo;
import org.dromara.common.servicetrack.domain.vo.*;
import org.dromara.common.servicetrack.service.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 项目管理 服务层实现
 *
 * <AUTHOR> <PERSON>
 */
@Slf4j
@Repository
public class ProjectDataServiceImpl implements IProjectDataService {

    private final IProjectInfoService projectInfoService;
    private final IProjectPageFieldServiceEx projectPageFieldService;
    private final IProjectFieldSelectionService projectFieldSelectionService;
    private final IWorkflowStateService workflowStateService;
    private final IWorkflowTransitionService workflowTransitionService;
    private final IProjectMemberService projectMemberService;
    private final MailAccountFactory mailAccountFactory;
    private final IProjectItemTypeService projectItemTypeService;
    public ProjectDataServiceImpl() {
        // 使用Spring注入的方式获取service instance
        this.projectInfoService = SpringUtils.getBean(IProjectInfoService.class);
        this.projectPageFieldService = SpringUtils.getBean(IProjectPageFieldServiceEx.class);
        this.projectFieldSelectionService = SpringUtils.getBean(IProjectFieldSelectionService.class);
        this.workflowStateService = SpringUtils.getBean(IWorkflowStateService.class);
        this.workflowTransitionService = SpringUtils.getBean(IWorkflowTransitionService.class);
        this.projectMemberService = SpringUtils.getBean(IProjectMemberService.class);
        this.mailAccountFactory = SpringUtils.getBean(MailAccountFactory.class);
        this.projectItemTypeService = SpringUtils.getBean(IProjectItemTypeService.class);
    }

    @Override
    public ProjectInfoVo selectProjectInfoById(Integer projectId) {
        var projectInfo = projectInfoService.selectProjectInfoById(projectId);
        //get email configuration
        try {
            var emailSetting = projectInfoService.getEmailSetting(projectId, eProjectSetting.Project_Email_Setting.getValue());
            if (emailSetting != null && !emailSetting.isEmpty()) {
                MailConfigDo mailConfig = JsonUtils.parseObject(emailSetting, MailConfigDo.class);
                if( mailConfig != null) {
                    mailConfig.setProjectName(projectInfo.getProjectName());
                    var encodePwd = mailConfig.getPass();
                    mailConfig.setPass(ValueConvert.decodeBase64(encodePwd));

                    projectInfo.setMailAccount(mailAccountFactory.createMailAccount(mailConfig));
                }
            }
        } catch (Exception e){
            log.error("邮件配置信息获取失败",e);
        }

        return projectInfo;
    }

    @Override
    public List<ProjectPageFieldVo> selectPageFieldList(ProjectPageFieldBo bo) {
       return projectPageFieldService.selectPageFieldList(bo,false);
    }

    @Override
    public List<ProjectFieldSelectionVo> selectFieldSelctionList(ProjectFieldSelectionBo bo) {
       return projectFieldSelectionService.selectFieldSelctionList(bo,false);
     }

    @Override
    public List<WorkflowStateVo> selectStateList(WorkflowStateBo bo) {
        return workflowStateService.selectStateList(bo, false);
    }

    @Override
    public List<WorkflowTransitionVo> selectTransitionList(WorkflowTransitionBo bo) {
        return workflowTransitionService.selectTransitionList(bo,false);
    }

    @Override
    public List<ProjectMemberVo> selectMemberList(Integer projectId) {
        return projectMemberService.selectMemberList(projectId);
    }

    @Override
    public List<SysUserSimpleInfoVo> selectSysUserList() {
        return projectMemberService.selectUserList();
    }

    @Override
    public String getDeptNameById(Long deptId) {
        return projectMemberService.getDeptNameById(deptId);
    }

    @Override
    public List<ProjectItemTypeVo> selectItemTypeList(Integer projectId) {
        return projectItemTypeService.selectItemTypeList(projectId);
    }

}
