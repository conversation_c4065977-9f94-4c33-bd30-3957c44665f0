package org.dromara.common.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.constant.eSystemPageDef;
import org.dromara.common.servicetrack.domain.ProjectPage;
import org.dromara.common.servicetrack.domain.ProjectPageField;
import org.dromara.common.servicetrack.domain.bo.ProjectInfoBo;
import org.dromara.common.servicetrack.domain.bo.ProjectPageBo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageVo;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.mapper.ProjectPageFieldMapper;
import org.dromara.common.servicetrack.mapper.ProjectPageMapper;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.service.IProjectPageService;
import org.springframework.stereotype.Service;
import   org.dromara.common.servicetrack.constant.stConstant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@RequiredArgsConstructor
@Service
public class ProjectPageServiceImpl implements IProjectPageService {
    private final ProjectPageMapper baseMapper;
    private final  TableSequenceManager sequenceManager;
    private final ProjectPageFieldMapper projectPageFieldMapper;

    @Override
    public ProjectPageVo queryById(Long keyId) {
        return baseMapper.selectVoById(keyId);
    }

    @Override
    public TableDataInfo<ProjectPageVo> queryPageList(Integer projectId, PageQuery pageQuery) {
        ProjectPageBo bo = new ProjectPageBo();
        bo.setProjectId(projectId);
        return TableDataInfo.<ProjectPageVo>build((com.baomidou.mybatisplus.core.metadata.IPage<ProjectPageVo>) baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(bo)));
    }

    @Override
    public List<ProjectPageVo> selectPageList(Integer projectId) {
        ProjectPageBo bo = new ProjectPageBo();
        bo.setProjectId(projectId);
        var pageList = baseMapper.selectVoList(this.buildQueryWrapper(bo));
        //Get all system page ids first
        List<Integer> existingSysPageIds = pageList.isEmpty() ? new ArrayList<>() :
            pageList.stream()
                .map(ProjectPageVo::getPageId)
                .filter(pageId -> pageId < stConstant.DEFAULT_CUSTOM_PAGE_ID)
                .toList();

        Boolean isBaseProject = ProjectManager.getInstance(projectId).isBaseProject();
        //need to add missing system pages
        List<ProjectPage> missingSysPages = new ArrayList<>();
        //Check all system page definitions
        for (eSystemPageDef systemPage : eSystemPageDef.values()) {
            //Skip if the system page already exists
            if (existingSysPageIds.contains(systemPage.getValue()) || systemPage.getValue() == eSystemPageDef.LastMax.getValue()) {
                continue;
            }
            ProjectPageVo sysPage = new ProjectPageVo();
            sysPage.setProjectId(projectId);
            sysPage.setPageId(systemPage.getValue());
            sysPage.setPageName(systemPage.getName());
            sysPage.setModuleId(1);
            pageList.add(sysPage);

            ProjectPageBo addBo = new ProjectPageBo();
            addBo.setProjectId(projectId);
            addBo.setPageId(systemPage.getValue());
            addBo.setPageName(systemPage.getName());
            addBo.setModuleId(1);
            var addPage = MapstructUtils.convert(addBo, ProjectPage.class);
            missingSysPages.add(addPage);
        }
        if (!missingSysPages.isEmpty() && !isBaseProject) {
            baseMapper.insertBatch(missingSysPages);
        }
        return baseMapper.selectVoList(this.buildQueryWrapper(bo));
    }

    private Wrapper<ProjectPage> buildQueryWrapper(ProjectPageBo bo) {
        QueryWrapper<ProjectPage> wrapper = Wrappers.query();
        wrapper.eq(bo.getProjectId() != null, "project_id", bo.getProjectId());
        wrapper.eq(bo.getPageId() != null, "page_id", bo.getPageId());
        wrapper.like(StringUtils.isNotBlank(bo.getPageName()), "page_name", bo.getPageName());
        wrapper.eq(bo.getModuleId() != null, "module_id", bo.getModuleId());
        return wrapper;
    }

    @Override
    public Integer insertByBo(ProjectPageBo bo) {
        int pageId = sequenceManager.getNextSequence(SequenceTable.Project_Page, bo.getProjectId());
        bo.setPageId(pageId);
        ProjectPage add = MapstructUtils.convert(bo, ProjectPage.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag ? bo.getPageId() : 0;
    }

    @Override
    public Integer updateByBo(ProjectPageBo bo) {
        ProjectPage update = MapstructUtils.convert(bo, ProjectPage.class);
        return baseMapper.updateById(update) > 0 ? bo.getPageId() : 0;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        //check if these page are used in project_page_field table
        // First get the page ids from Project Page table
        var pageList = baseMapper.selectVoByIds(keyIds);
        var pageIds = pageList.stream().map(ProjectPageVo::getPageId).toList();
        if (pageIds.isEmpty()) {
            return true;
        }
        var sysPageIds = pageIds.stream().filter(pageId -> pageId < stConstant.DEFAULT_CUSTOM_PAGE_ID).toList();
        if (!sysPageIds.isEmpty()) {
            throw new ServiceException("不能删除系统页面！");
        }
        QueryWrapper<ProjectPageField> wrapper = new QueryWrapper<>();
        wrapper.in("page_id", pageIds);
        if (projectPageFieldMapper.selectCount(wrapper) > 0) {
            throw new ServiceException("有些字段正在使用中,不能删除！");
        }
        return baseMapper.deleteByIds(keyIds) > 0;
    }
}
