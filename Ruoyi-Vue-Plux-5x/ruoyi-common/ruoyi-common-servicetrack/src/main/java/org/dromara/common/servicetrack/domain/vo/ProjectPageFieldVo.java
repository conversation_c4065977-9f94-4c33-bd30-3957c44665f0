package org.dromara.common.servicetrack.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.servicetrack.domain.ProjectPageField;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 页面字段对象VO
 *
 * <AUTHOR> fei
 */
@Data
@NoArgsConstructor
@AutoMapper(target = ProjectPageField.class)
public class ProjectPageFieldVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * project id
     */
    @JsonIgnore
    private Integer projectId;

    /**
     * page id
     */
    private Integer pageId;

    /**
     * page name
     */
    private String pageName;

    /**
     * field id
     */
    private Integer fieldId;

    /**
     * field name
     */
    private String fieldName;

    /**
     * field type
     */
    private Integer fieldType;

    /**
     * page row
     */
    private Integer pageRow;

    /**
     * page column
     */
    private Integer pageColumn;

    /**
     *  module id
     */
    private Integer moduleId;
}
