package org.dromara.common.servicetrack.constant;

public enum eSystemFieldDef implements IValueEnum, IBaseFieldEnum {
    Transition(-3),
    NONE(0),
    Title(1),
    Description(2),
    Status(3),
    Owner(4),
    Type(5),
    Employee(6),
    Asset(7),
    Attachment(8),
    Solution(9),
    IncidentID(10),
    SLAStartTime(11),
    RequestRespondTime(12),
    RequestResolvedTime(13),
    ActualRespondTime(14),
    ActualResolvedTime(15),
    Knowledge(16),
    Evaluation(17),
    SubmittedTime(18),
    LastModifiedTime(19),
    SubmittedBy(20),
    LastModifiedBy(21);


    private final int value;
    /*
     *构造函数在定义常量时自动调用
     */
    eSystemFieldDef(int value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return value;
    }
    @Override
    public String getName() {
        // TODO Auto-generated method stub
        return switch (this.value) {
            case 1 -> "标题";//"Title";
            case 2 -> "描述";//"Description";
            case 3 -> "状态";//"Status";
            case 4 -> "负责人";//"Owner";
            case 5 -> "类型";//"Type";
            case 6 -> "员工";//"Employee";
            case 7 -> "资产";//"Asset";
            case 8 -> "附件";//"Attachment";
            case 9 -> "解决方案";//"Solution";
            case 10 -> "事件ID";//"IncidentID";
            case 11 -> "SLA开始时间";//"SLAStartTime";
            case 12 -> "请求响应时间";//"RequestRespondTime";
            case 13 -> "请求解决时间";//"RequestResolvedTime";
            case 14 -> "实际响应时间";//"ActualRespondTime";
            case 15 -> "实际解决时间";//"ActualResolvedTime";
            case 16 -> "知识库";//"Knowledge";
            case 17 -> "评价";//"Evaluation";
            case 18 -> "提交时间";//"SubmittedTime";
            case 19 -> "最后修改时间";//"LastModifiedTime";
            case 20 -> "提交者";//"SubmittedBy";
            case 21 -> "修改者";//"LastModifiedBy";
            default -> "";
        };
    }
    @Override
    public String getDefaultName() {
        return this.name();
    }
    @Override
    public Integer getFieldType() {
        return switch (this.value) {
            case 1 -> 1;//"Title";
            case 2 -> 6;//"Description";
            case 3 -> 3;//"Status";
            case 4 -> 3;//"Owner";
            case 5 -> 3;//"Type";
            case 6 -> 99;//"Employee";
            case 7 -> 99;//"Asset";
            case 8 -> 99;//"Attachment";
            case 9 -> 6;//"Solution";
            case 10 -> 1;//"IncidentID";
            case 11 -> 5;//"SLAStartTime";
            case 12 -> 5;//"RequestRespondTime";
            case 13 -> 5;//"RequestResolvedTime";
            case 14 -> 5;//"ActualRespondTime";
            case 15 -> 5;//"ActualResolvedTime";
            case 16 -> 99;//"Knowledge";
            case 17 -> 99;//"Evaluation";
            case 18 -> 5;//"SubmittedTime";
            case 19 -> 5;//"LastModifiedTime";
            case 20 -> 3;//"SubmittedBy";
            default -> 1;
        };
    }
    public static eSystemFieldDef from(Integer value) {
        return IValueEnum.valueOf(eSystemFieldDef.class, value);
    }
}
