package org.dromara.common.servicetrack.logic.fieldvalue;

import org.dromara.common.servicetrack.constant.eCultureCode;
import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.logic.project.ProjectManager;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class MultiSelectionListFieldValue extends BaseFieldValue{
    private final Integer projectId;
    private final Integer fieldId;
    private List<Integer> choiceIds;

    public  MultiSelectionListFieldValue(Integer projectId, Integer fieldId) {
        this.projectId = projectId;
        this.fieldId = fieldId;
    }
    @Override
    public Boolean equalsTo(IFieldValue other) {
        if (other == null) {
            return false;
        }
        return equals2((MultiSelectionListFieldValue) other);
    }
    public Boolean equals2(MultiSelectionListFieldValue other)
    {
        if (other == null)
            return false;
        else if (choiceIds == null && other.choiceIds == null)
            return true;
        else if (choiceIds == null || other.choiceIds == null)
            return false;
        else {
            Set<Integer> unionSet = new HashSet<>(choiceIds);
            unionSet.addAll(other.choiceIds);
            return choiceIds.size() == other.choiceIds.size() && unionSet.size() == choiceIds.size();
        }
    }
    @Override
    public String getDisplayValue() {
        if( choiceIds != null){
            ProjectManager helper = ProjectManager.getInstance(projectId);
            return helper.getFieldChoiceNames(fieldId, choiceIds, eCultureCode.DEFAULT);
        }
        return "";
    }

    @Override
    public Object getRawValue() {
        return choiceIds;
    }

    @Override
    public void readValueFromDB(Object data) {
        if( data != null){
            if( data instanceof String){
                String[] items = ((String) data).split(",");
                choiceIds = new ArrayList<>();
                ProjectManager helper = ProjectManager.getInstance(projectId);
                for(String item : items){
                    Integer  id = helper.getChoiceIdByName(fieldId, item);
                    if( id != 0)
                        choiceIds.add(id);
                }
            }
           else if (data instanceof List<?> listData) {
                choiceIds = new ArrayList<>();
                for (Object item : listData) {
                    if (item instanceof Integer) {
                        choiceIds.add((Integer) item);
                    } else if (item instanceof String) {
                        choiceIds.add(Integer.parseInt((String) item));
                    }
                }
            }
        }
    }

    @Override
    public void setFieldValue(Object value, Integer option) {
        if( value == null){
            choiceIds = null;
        }
        else if( value instanceof List<?> listData){
            choiceIds = new ArrayList<>();
            for(Object item : listData){
                if( item instanceof Integer){
                    choiceIds.add((Integer)item);
                }
                else if( item instanceof String){
                    choiceIds.add(Integer.parseInt((String)item));
                }
            }
        }
        else if( value instanceof String){
            if( value.toString().isEmpty()){
                choiceIds = null;
                return;
            }
            String[] ids = ((String) value).split(",");
            choiceIds = new ArrayList<>();
            for(String id : ids){
                choiceIds.add(Integer.parseInt(id));
            }
        }
    }

    @Override
    public String toCustomFieldFormatString() {
        if( choiceIds != null){
            return getDisplayValue();
        }

        return "";
    }

    @Override
    public Boolean isUnassigned() {
        return choiceIds == null || choiceIds.isEmpty();
    }
}
