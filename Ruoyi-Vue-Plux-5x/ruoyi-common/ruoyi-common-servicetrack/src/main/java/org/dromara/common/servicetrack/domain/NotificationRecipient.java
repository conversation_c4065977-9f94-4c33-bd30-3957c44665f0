package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 通知接收人对象 notification_recipient
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("notification_recipient")
public class NotificationRecipient extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long keyId;

    /**
     * 项目ID
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * 规则ID
     */
    @TableField(value = "rule_id")
    private Integer ruleId;

    /**
     * 用户类型
     */
    @TableField(value = "user_type")
    private Integer userType;

    /**
     * 用户ID
     */
    @TableField(value = "user_id")
    private Integer userId;
}
