package org.dromara.common.servicetrack.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.servicetrack.domain.NotificationEmailTemplate;
import org.dromara.common.servicetrack.domain.bo.NotificationEmailTemplateBo;
import org.dromara.common.servicetrack.domain.vo.NotificationEmailTemplateVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 通知邮件模板管理 数据层
 *
 * <AUTHOR>
 */
public interface NotificationEmailTemplateMapper extends BaseMapperPlus<NotificationEmailTemplate, NotificationEmailTemplateVo> {
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<NotificationEmailTemplate> buildWrapper(NotificationEmailTemplateBo bo) {
        LambdaQueryWrapper<NotificationEmailTemplate> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getKeyId() != null, NotificationEmailTemplate::getKeyId, bo.getKeyId());
        lqw.eq(bo.getProjectId() != null, NotificationEmailTemplate::getProjectId, bo.getProjectId());
        lqw.eq(bo.getTemplateId() != null, NotificationEmailTemplate::getTemplateId, bo.getTemplateId());
        lqw.like(StringUtils.isNotBlank(bo.getEmailSubject()), NotificationEmailTemplate::getEmailSubject, bo.getEmailSubject());
        return lqw;
    }

    /**
     * 根据项目ID和模板ID查询邮件模板
     *
     * @param projectId  项目ID
     * @param templateId 模板ID
     * @return 邮件模板
     */
    NotificationEmailTemplateVo selectByProjectAndTemplate(@Param("projectId") Integer projectId, @Param("templateId") Integer templateId);

    /**
     * 查询项目下的所有邮件模板
     *
     * @param projectId 项目ID
     * @return 邮件模板列表
     */
    List<NotificationEmailTemplateVo> selectByProjectId(@Param("projectId") Integer projectId);
}
