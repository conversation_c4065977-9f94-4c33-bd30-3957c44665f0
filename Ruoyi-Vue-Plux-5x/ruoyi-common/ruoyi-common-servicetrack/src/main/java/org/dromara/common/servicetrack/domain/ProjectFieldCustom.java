package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * 项目自定义字段实体类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_field_custom")
public class ProjectFieldCustom extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long Id;

    /**
     * 项目ID
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * 字段ID
     */
    @TableField(value = "field_id")
    private Integer fieldId;

    /**
     * 字段名称
     */
    @TableField(value = "field_name")
    private String fieldName;

    /**
     * 模块ID
     */
    @TableField(value = "module_id")
    private Integer moduleId;

    /**
     * 字段类型
     */
    @TableField(value = "field_type")
    private Integer fieldType;
}
