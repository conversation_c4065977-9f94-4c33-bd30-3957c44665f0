package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ProjectFieldSelection;
import org.dromara.common.servicetrack.domain.bo.ProjectFieldSelectionBo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldSelectionVo;

import java.util.List;

/**
 * 字段选项 数据层
 *
 * <AUTHOR> fei
 */
@Mapper
public interface ProjectFieldSelectionMapper extends  BaseMapperPlus<ProjectFieldSelection, ProjectFieldSelectionVo>  {

    default LambdaQueryWrapper<ProjectFieldSelection> buildWrapper(ProjectFieldSelectionBo bo) {
        LambdaQueryWrapper<ProjectFieldSelection> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, ProjectFieldSelection::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, ProjectFieldSelection::getProjectId, bo.getProjectId());
        lqw.eq(bo.getFieldId() != null , ProjectFieldSelection::getFieldId, bo.getFieldId());
        lqw.like(StringUtils.isNotBlank(bo.getChoiceName()), ProjectFieldSelection::getChoiceName, bo.getChoiceName());
       lqw.eq(bo.getChoiceId() != null , ProjectFieldSelection::getChoiceId, bo.getChoiceId());

        return lqw;
    }
    /**
     * Check if field selections are used in item_selection table
     *
     * @param projectId Project ID
     * @param fieldIds Field IDs
     * @param choiceIds Choice IDs
     * @return ProjectFieldSelectionVo containing count of selections in use
     */
    ProjectFieldSelectionVo selectFieldSelectionByItemSelection(@Param("projectId") Integer projectId,
                                                                @Param("fieldIds") List<Integer> fieldIds,
                                                                @Param("choiceIds") List<Integer> choiceIds);
}

