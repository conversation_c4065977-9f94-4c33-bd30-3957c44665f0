package org.dromara.common.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.domain.ProjectAccountType;
import org.dromara.common.servicetrack.domain.bo.ProjectAccountTypeBo;
import org.dromara.common.servicetrack.domain.vo.ProjectAccountTypeVo;
import org.dromara.common.servicetrack.mapper.ProjectAccountTypeMapper;
import org.dromara.common.servicetrack.service.IProjectAccountTypeService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 项目账户类型 服务层实现
 *
 * <AUTHOR> Fei
 */
@RequiredArgsConstructor
@Service
public class ProjectAccountTypeServiceImpl implements IProjectAccountTypeService {
    private final ProjectAccountTypeMapper baseMapper;
    private final TableSequenceManager tableSequenceManager;
    /**
     * 查询项目账户类型
     */
    @Override
    public ProjectAccountTypeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public ProjectAccountTypeVo selectProjectAccountTypeById(Integer projectId, Integer accountTypeId) {
        return null;
    }

    /**
     * 查询项目账户类型列表
     */
    @Override
    public TableDataInfo<ProjectAccountTypeVo> queryPageList(Integer projectId,PageQuery pageQuery) {
        ProjectAccountTypeBo bo = new ProjectAccountTypeBo();
        bo.setProjectId(projectId);
        return TableDataInfo.<ProjectAccountTypeVo>build((com.baomidou.mybatisplus.core.metadata.IPage<ProjectAccountTypeVo>) baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(bo)));
    }

    @Override
    public List<ProjectAccountTypeVo> queryList(ProjectAccountTypeBo bo) {
        return baseMapper.selectVoList(this.buildQueryWrapper(bo));
    }

    private Wrapper<ProjectAccountType> buildQueryWrapper(ProjectAccountTypeBo bo) {
        QueryWrapper<ProjectAccountType> wrapper = Wrappers.query();
        wrapper.eq(bo.getProjectId() != null, "project_id", bo.getProjectId());
        wrapper.eq(bo.getAccountTypeId() != null, "accounttype_id", bo.getAccountTypeId());
        wrapper.eq(bo.getTypeId() != null, "type_id", bo.getTypeId());
        wrapper.like(StringUtils.isNotBlank(bo.getAccounttypeName()), "accounttype_name", bo.getAccounttypeName());
        return wrapper;
    }


    /**
     * 新增项目账户类型
     */
    @Override
    public Integer insertByBo(ProjectAccountTypeBo bo) {
        int accountTypeId  = tableSequenceManager.getNextSequence(SequenceTable.Project_AccountType, bo.getProjectId());
        bo.setAccountTypeId(accountTypeId);
        ProjectAccountType add = MapstructUtils.convert(bo, ProjectAccountType.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag ? add.getAccountTypeId() : 0;
    }

    /**
     * 修改项目账户类型
     */
    @Override
    public Integer updateByBo(ProjectAccountTypeBo bo) {
        ProjectAccountType update = MapstructUtils.convert(bo, ProjectAccountType.class);
        return baseMapper.updateById(update) > 0 ? update.getAccountTypeId() : 0;
    }

    /**
     * 批量删除项目账户类型
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
