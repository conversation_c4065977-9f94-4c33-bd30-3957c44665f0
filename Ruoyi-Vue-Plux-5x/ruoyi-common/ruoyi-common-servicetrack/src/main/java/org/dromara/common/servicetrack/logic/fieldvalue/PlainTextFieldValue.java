package org.dromara.common.servicetrack.logic.fieldvalue;

import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.utils.ValueConvert;

import java.util.Objects;

public class PlainTextFieldValue extends BaseFieldValue{
    protected String rawValue;
    @Override
    public Boolean equalsTo(IFieldValue other) {
        return equals2( (PlainTextFieldValue)other);
    }
    private Boolean equals2(PlainTextFieldValue other) {
        if (other == null) {
            return false;
        } else {
            return Objects.equals(rawValue, other.rawValue);
        }
    }
    @Override
    public String getDisplayValue() {
        return rawValue;
    }

    @Override
    public Object getRawValue() {
        return rawValue;
    }

    @Override
    public void readValueFromDB(Object data) {
        rawValue = ValueConvert.readString(data);
    }

    @Override
    public void setFieldValue(Object value, Integer option) {
        if (value == null)
            rawValue = null;
        else
            rawValue = ValueConvert.readString(value);
    }

    @Override
    public String toCustomFieldFormatString() {
        return rawValue;
    }

    @Override
    public Boolean isUnassigned() {
        return rawValue == null || rawValue.isEmpty();
    }
}
