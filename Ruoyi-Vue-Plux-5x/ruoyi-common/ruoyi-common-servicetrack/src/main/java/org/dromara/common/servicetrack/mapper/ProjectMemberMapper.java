package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jodd.introspector.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.ProjectInfo;
import org.dromara.common.servicetrack.domain.ProjectMember;
import org.dromara.common.servicetrack.domain.bo.ProjectInfoBo;
import org.dromara.common.servicetrack.domain.bo.ProjectMemberBo;
import org.dromara.common.servicetrack.domain.vo.ProjectInfoVo;
import org.dromara.common.servicetrack.domain.vo.ProjectMemberVo;
import org.dromara.common.servicetrack.domain.vo.SysUserSimpleInfoVo;

import java.util.List;

/**
 * 项目成员管理 数据层
 *
 * <AUTHOR> fei
 */
public interface ProjectMemberMapper extends BaseMapperPlus<ProjectMember, ProjectMemberVo> {

    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ProjectMember> buildWrapper(ProjectMemberBo bo) {
        LambdaQueryWrapper<ProjectMember> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProjectId() != null, ProjectMember::getProjectId, bo.getProjectId());

        return lqw;
    }

    /**
     * 查询项目成员列表
     */
    List<ProjectMemberVo> selectMemberList(@Param("projectId") Integer projectId);
    /**
     * 分页查询项目成员列表
     */
    Page<ProjectMemberVo> selectMemberListByPage(@Param("page") Page<ProjectMemberVo> page,@Param("projectId") Integer projectId);
    /**
     * 获得所有用户列表
     */
    List<SysUserSimpleInfoVo> selectUserList();

    /**
     * 根据部门ID查询部门名称
     */
    String getDeptNameById(@Param("deptId") Long deptId);
}
