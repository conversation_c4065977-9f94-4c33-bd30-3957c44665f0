package org.dromara.common.servicetrack.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.ProjectPageAction;
import org.dromara.common.servicetrack.domain.bo.ProjectPageActionBo;
import org.dromara.common.servicetrack.domain.bo.ProjectPageActionSortBo;
import org.dromara.common.servicetrack.domain.bo.ProjectSortBinderBo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageActionVo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageActonModelVo;
import org.dromara.common.servicetrack.mapper.ProjectPageActionMapper;
import org.dromara.common.servicetrack.service.IProjectPageActionService;
import org.springframework.stereotype.Service;

import java.util.*;

@RequiredArgsConstructor
@Service
public class ProjectPageActionServiceImpl implements IProjectPageActionService {
    private final ProjectPageActionMapper baseMapper;

    @Override
    public ProjectPageActionVo queryById(Long keyId) {
        return baseMapper.selectVoById(keyId);
    }

    @Override
    public TableDataInfo<ProjectPageActionVo> queryPageList(Integer projectId, PageQuery pageQuery) {
        ProjectPageActionBo bo = new ProjectPageActionBo();
        bo.setProjectId(projectId);
        return TableDataInfo.<ProjectPageActionVo>build((com.baomidou.mybatisplus.core.metadata.IPage<ProjectPageActionVo>) baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(bo)));
    }

    @Override
    public List<ProjectPageActonModelVo> selectPageActionModelList(Integer projectId) {
        var pageActionVoList = baseMapper.selectPageActionList(projectId);
        if (pageActionVoList.isEmpty()) {
            return List.of();
        }
        // 使用 Map 按 typeId 分类
        Map<Integer, ProjectPageActonModelVo> groupedByTypeId = new HashMap<>();
        for (ProjectPageActionVo pageActionVo : pageActionVoList) {
            Integer typeId = pageActionVo.getTypeId();

            // 如果 Map 中还没有该 typeId 的对象，则创建一个新的
            ProjectPageActonModelVo modelVo = groupedByTypeId.computeIfAbsent(typeId, k -> {
                ProjectPageActonModelVo newModel = new ProjectPageActonModelVo();
                newModel.setTypeId(pageActionVo.getTypeId());
                newModel.setTypeName(pageActionVo.getTypeName());
                newModel.setTransitionId(pageActionVo.getTransitionId());
                newModel.setItemTypePageActions(new ArrayList<>());
                return newModel;
            });

            // 创建并添加 PageActionInfoVo
            ProjectPageActonModelVo.ProjectPageActionInfoVo pageActionInfo = new ProjectPageActonModelVo.ProjectPageActionInfoVo();
            pageActionInfo.setPageId(pageActionVo.getPageId());
            pageActionInfo.setActionId(pageActionVo.getActionId());
            pageActionInfo.setPageOrder(pageActionVo.getPageOrder());
            modelVo.getItemTypePageActions().add(pageActionInfo);
        }
        // 将 Map 的值转换为列表
        return new ArrayList<>(groupedByTypeId.values());
    }

    @Override
    public List<ProjectPageActionVo> selectPageActionList(Integer projectId) {
        return baseMapper.selectPageActionList(projectId);
    }

    private Wrapper<ProjectPageAction> buildQueryWrapper(ProjectPageActionBo bo) {
        QueryWrapper<ProjectPageAction> wrapper = Wrappers.query();
        wrapper.eq(bo.getProjectId() != null, "project_id", bo.getProjectId());
        wrapper.eq(bo.getItemTypeId() != null, "itemtype_id", bo.getItemTypeId());
        wrapper.eq(bo.getPageId() != null, "page_id", bo.getPageId());
        wrapper.eq(bo.getActionId() != null, "action_id", bo.getActionId());
        wrapper.eq(bo.getPageOrder() != null, "page_order", bo.getPageOrder());
        return wrapper;
    }

    @Override
    public Integer insertByBo(ProjectPageActionBo bo) {
        //get the max page_order using MAX function
        QueryWrapper<ProjectPageAction> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("COALESCE(MAX(page_order), 0) as page_order")
            .eq(ObjectUtil.isNotNull(bo.getProjectId()), "project_id", bo.getProjectId())
            .eq(ObjectUtil.isNotNull(bo.getItemTypeId()), "itemtype_id", bo.getItemTypeId());
            queryWrapper.eq(ObjectUtil.isNotNull(bo.getActionId()), "action_id", bo.getActionId());
        Integer maxPageOrder = baseMapper.selectOne(queryWrapper).getPageOrder();
        bo.setPageOrder(maxPageOrder + 1);
        ProjectPageAction add = MapstructUtils.convert(bo, ProjectPageAction.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag ? 1 : 0;
    }

    @Override
    public Integer updateByBo(ProjectPageActionBo bo) {
        ProjectPageAction update = MapstructUtils.convert(bo, ProjectPageAction.class);
        return baseMapper.updateById(update) > 0 ? 1 : 0;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        return baseMapper.deleteByIds(keyIds) > 0;
    }

    @Override
    public Boolean sortPageAction(ProjectPageActionSortBo bo) {
        if(bo.getPageIds()== null || bo.getPageIds().isEmpty()) {
            throw new ServiceException("page Ids is null or empty");
        }
        var pageActions = baseMapper.selectList(new QueryWrapper<ProjectPageAction>().lambda().eq(ProjectPageAction::getProjectId, bo.getProjectId())
            .eq(ProjectPageAction::getItemTypeId, bo.getItemTypeId())
            .eq(ProjectPageAction::getActionId, bo.getActionId())
            .in(ProjectPageAction::getPageId, bo.getPageIds()));

        var pageOrder = 1;
        List<ProjectPageAction> pageActionsList = new ArrayList<>();
        for (var pageId : bo.getPageIds()) {
            var pageAction = pageActions.stream().filter(p -> p.getPageId().equals(pageId)).findFirst().orElse(null);
            if (pageAction == null) {
                continue;
            }
            pageAction.setPageOrder(pageOrder++);
            pageActionsList.add(pageAction);
        }
        if (!pageActionsList.isEmpty()) {
           return  baseMapper.updateBatchById(pageActionsList);
        }
        return true;
    }
}
