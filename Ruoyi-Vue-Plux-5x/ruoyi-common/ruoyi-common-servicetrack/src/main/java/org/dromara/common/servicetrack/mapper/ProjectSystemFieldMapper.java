package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ProjectSystemField;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldVo;
import org.dromara.common.servicetrack.domain.vo.ProjectSystemFieldVo;

import java.util.List;

/**
 * 字段管理 数据层
 *
 * <AUTHOR> f<PERSON>
 */

public interface ProjectSystemFieldMapper extends BaseMapperPlus<ProjectSystemField, ProjectSystemFieldVo>  {


    @DataPermission({
        @DataColumn(key = "fieldName", value = "field_id")
    })
    List<ProjectFieldVo> selectFieldList(@Param(Constants.WRAPPER) Wrapper<ProjectSystemField> queryWrapper);
}
