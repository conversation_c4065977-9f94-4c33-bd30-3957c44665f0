package org.dromara.common.servicetrack.domain.bo;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * 项目页面字段行移动对象Bo
 *
 * <AUTHOR> fei
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectPageFieldMoveRowBo extends STBaseEntity {
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    @NotNull(message = "页面ID不能为空")
    private Integer pageId;

    @NotNull(message = "页面移动Row不能为空")
    @Min(value = 1, message = "页面移动Row不能小于1")
    private Integer movingRow;

    @NotNull(message = "页面移动目标Row不能为空")
    @Min(value = 1, message = "页面移动目标Row不能小于1")
    private Integer targetRow;
}
