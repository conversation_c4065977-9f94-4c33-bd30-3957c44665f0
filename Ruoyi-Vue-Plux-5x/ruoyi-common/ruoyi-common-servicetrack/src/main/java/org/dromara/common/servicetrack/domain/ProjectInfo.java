package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
/**
 * 项目对象 Project_Info
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_info")
public class ProjectInfo  extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * Project type
     */
    @TableField(value = "project_type")
    private  Integer projectType;

    /**
     * Project name
     */
    @TableField(value = "project_name")
    private String projectName;

    /**
     * Project key
     */
    @TableField(value = "project_key")
    private String projectKey;

    /**
     * Is active
     */
    @TableField(value = "isactive")
    private Integer isActive;

    /**
     * base project id
     */
    @TableField(value = "baseproject_id")
    private Integer baseProjectId;
}
