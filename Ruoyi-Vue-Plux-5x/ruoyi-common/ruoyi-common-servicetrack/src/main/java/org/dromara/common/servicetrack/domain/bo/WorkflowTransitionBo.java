package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.WorkflowTransition;

import java.util.Date;

/**
 * 工作流转换Bo
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WorkflowTransition.class)
public class WorkflowTransitionBo extends STBaseEntity {
    /**
     * ID
     */
    //@NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * transition_id
     */
    private Integer transitionId;

    /**
     * transition_name
     */
    private String transitionName;

    /**
     * from_state_id
     * /
     */
    private Integer fromStateId;

    /**
     * to_state_id
     */
    private Integer toStateId;

    /**
     * display_order
     */
    private Integer displayOrder;
}
