package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ProjectSystemField;

import java.util.Date;

/**
 * 系统字段业务对象 project_system_field
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectSystemField.class)
public class ProjectSystemFieldBo extends STBaseEntity {
    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空", groups = { AddGroup.class , EditGroup.class })
    private Integer projectId;

    /**
     * 字段ID
     */
    @NotNull(message = "字段ID不能为空", groups = { AddGroup.class , EditGroup.class })
    private Integer fieldId;

    /**
     * 字段名称
     */
    @NotNull(message = "字段名称不能为空", groups = { AddGroup.class , EditGroup.class })
    @Xss(message = "字段名称不能包含脚本字符")
    @NotBlank(message = "字段名称不能为空")
    @Size(min = 0, max = 200, message = "字段名称的长度不能超过{max}")
    private String fieldName;

    /**
     * 字段类型
     */
    @NotNull(message = "字段类型不能为空", groups = { AddGroup.class , EditGroup.class })
    private Integer fieldType;

    /**
     * 模块ID
     */
    @NotNull(message = "模块ID不能为空", groups = { AddGroup.class , EditGroup.class })
    private Integer moduleId;

    /**
     * 字段默认名称
     */
    @NotNull(message = "字段默认名称不能为空", groups = { AddGroup.class })
    @Xss(message = "字段默认名称不能包含脚本字符")
    @NotBlank(message = "字段默认名称不能为空")
    @Size(min = 0, max = 200, message = "字段默认名称的长度不能超过{max}")
    private String fieldDefaultName;

}
