package org.dromara.common.servicetrack.sequence;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 表序列号管理器
 *
 * <AUTHOR>
 */
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;
@Slf4j
@Component
public class TableSequenceManager {

    // 序列号缓存 Map<表, Map<父ID, 序列号>> 父ID为0表示无父ID
    private final ConcurrentHashMap<SequenceTable, ConcurrentHashMap<Integer, AtomicInteger>> sequenceCache;
    private final SequenceRepository sequenceRepository;

    @Autowired
    public TableSequenceManager(SequenceRepository sequenceRepository) {
        this.sequenceRepository = sequenceRepository;
        this.sequenceCache = new ConcurrentHashMap<>();
    }

    /**
     * 初始化缓存 - Spring容器启动时自动调用
     */
    @PostConstruct
    public void init() {
        log.info("Initializing sequence cache");
        try {
            // 准备批量查询参数
            List<Map<String, String>> tableInfos = new ArrayList<>();
            for (SequenceTable table : SequenceTable.values()) {
                TableDefinition definition = TableDefinition.get(table);
                //get tables info without parent id
                if (!definition.supportsParentId()){
                    Map<String, String> tableInfo = new HashMap<>();
                    tableInfo.put("tableName", definition.getTableName());
                    tableInfo.put("sequenceColumn", definition.getSequenceColumn());
                    tableInfos.add(tableInfo);
                }
            }

            // 执行批量查询获取无父ID的最大序列号
            List<Map<String, Object>> results = sequenceRepository.getMaxSequences(tableInfos);

            // 处理查询结果
            for (Map<String, Object> result : results) {
                String tableName = (String) result.get("tableName");
                Integer maxValue = ((Number) result.get("maxSequence")).intValue();

                // 找到对应的SequenceTable枚举
                for (SequenceTable table : SequenceTable.values()) {
                    TableDefinition definition = TableDefinition.get(table);
                    if (definition.getTableName().equals(tableName)) {
                        ConcurrentHashMap<Integer, AtomicInteger> tableCache = new ConcurrentHashMap<>();
                        tableCache.put(0, new AtomicInteger(maxValue)); // 0表示无父ID
                        sequenceCache.put(table, tableCache);
                        log.info("Initialized {} with max value: {}", table, maxValue);
                        break;
                    }
                }
            }

            // 准备批量查询参数
            List<Map<String, String>> parentTableInfos = new ArrayList<>();
            for (SequenceTable table : SequenceTable.values()) {
                TableDefinition definition = TableDefinition.get(table);
                if (definition.supportsParentId()) {
                    Map<String, String> tableInfo = new HashMap<>();
                    tableInfo.put("tableName", definition.getTableName());
                    tableInfo.put("sequenceColumn", definition.getSequenceColumn());
                    tableInfo.put("parentIdColumn", definition.getParentIdColumn());
                    parentTableInfos.add(tableInfo);
                }
            }

            // 执行批量查询获取所有支持父ID的表的最大序列号
            List<Map<String, Object>> parentResults = sequenceRepository.getAllParentMaxSequences(parentTableInfos);

            // 遍历所有支持父ID的表，初始化缓存
            for (SequenceTable table : SequenceTable.values()) {
                TableDefinition definition = TableDefinition.get(table);
                if (definition.supportsParentId()) {
                    // 初始化表的缓存
                    ConcurrentHashMap<Integer, AtomicInteger> tableCache = sequenceCache.computeIfAbsent(
                        table, k -> new ConcurrentHashMap<>());

                    // 检查 parentResults 中是否有该表的记录
                    boolean found = false;
                    for (Map<String, Object> result : parentResults) {
                        String tableName = (String) result.get("tableName");
                        if (definition.getTableName().equals(tableName)) {
                            int parentId = ((Number) result.get("parentId")).intValue();
                            int maxValue = ((Number) result.get("maxSequence")).intValue();
                            tableCache.put(parentId, new AtomicInteger(maxValue));
                            log.info("Initialized {} with parentId={}, max value: {}", table, parentId, maxValue);
                            found = true;
                        }
                    }

                    // 如果没有找到记录，则补上一条默认记录
                    if (!found) {
                        tableCache.put(0, new AtomicInteger(0)); // 0 表示默认的父ID
                        log.info("Initialized {} with default value: 0", table);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Failed to initialize sequence cache", e);
            throw new RuntimeException("Failed to initialize sequence manager", e);
        }
    }

    /**
     * 获取下一个序列号
     */
    public int getNextSequence(SequenceTable table) {
        return getNextSequence(table, 0);
    }

    /**
     * 获取带父ID的下一个序列号
     */
    public int getNextSequence(SequenceTable table, int parentId) {
        TableDefinition definition = TableDefinition.get(table);
        if (parentId != 0 && !definition.supportsParentId()) {
            throw new IllegalArgumentException("Table " + table + " does not support parent ID");
        }

        ConcurrentHashMap<Integer, AtomicInteger> tableCache = sequenceCache.get(table);
        if (tableCache == null) {
            throw new IllegalStateException("Sequence not initialized for table: " + table);
        }

        AtomicInteger sequence = tableCache.get(parentId);
        if (sequence == null) {
            // 如果缓存中没有该父ID的序列号,则从数据库获取
            int maxSeq = 0;
            if (parentId != 0) {
                maxSeq = sequenceRepository.getMaxSequenceByParent(
                    definition.getTableName(),
                    definition.getSequenceColumn(),
                    definition.getParentIdColumn(),
                    parentId
                );
                if(table.equals(SequenceTable.Project_Field_Custom) && maxSeq == 0){
                    maxSeq = 10000;
                }
                else if( table.equals(SequenceTable.Project_Page) && maxSeq == 0){
                    maxSeq = 500;
                }
            }
            sequence = new AtomicInteger(maxSeq);
            tableCache.put(parentId, sequence);
        }

        return sequence.incrementAndGet();
    }

    /**
     * 获取当前序列号值（不递增）
     */
    public int getCurrentSequence(SequenceTable table) {
        return getCurrentSequence(table, 0);
    }

    /**
     * 获取带父ID的当前序列号值（不递增）
     */
    public int getCurrentSequence(SequenceTable table, int parentId) {
        TableDefinition definition = TableDefinition.get(table);
        if (parentId != 0 && !definition.supportsParentId()) {
            throw new IllegalArgumentException("Table " + table + " does not support parent ID");
        }

        ConcurrentHashMap<Integer, AtomicInteger> tableCache = sequenceCache.get(table);
        if (tableCache == null) {
            throw new IllegalStateException("Sequence not initialized for table: " + table);
        }

        AtomicInteger sequence = tableCache.get(parentId);
        if (sequence == null) {
            return 0;
        }

        return sequence.get();
    }
}
