package org.dromara.common.servicetrack.service;

import org.dromara.common.servicetrack.domain.bo.WorkflowTransitionBo;
import org.dromara.common.servicetrack.domain.vo.WorkflowTransitionVo;

import java.util.List;

/**
 * 工作流转变管理 服务层
 *
 * <AUTHOR> fei
 * */
public interface IWorkflowTransitionService {
    List<WorkflowTransitionVo> selectTransitionList(WorkflowTransitionBo bo, boolean fromCache);
}
