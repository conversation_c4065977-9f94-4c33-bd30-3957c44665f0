package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 通知邮件模板对象 notification_email_template
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("notification_email_template")
public class NotificationEmailTemplate extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long keyId;

    /**
     * 项目ID
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * 模板ID
     */
    @TableField(value = "template_id")
    private Integer templateId;

    /**
     * 邮件主题
     */
    @TableField(value = "email_subject")
    private String emailSubject;

    /**
     * 邮件内容
     */
    @TableField(value = "email_body")
    private String emailBody;
}
