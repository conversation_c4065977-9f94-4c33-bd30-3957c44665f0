package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.NotificationEmailTemplate;

/**
 * 通知邮件模板业务对象 notification_email_template
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = NotificationEmailTemplate.class)
public class NotificationEmailTemplateBo extends STBaseEntity {
    /**
     * ID
     */
    private Long keyId;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 模板ID
     */
    @NotNull(message = "模板ID不能为空")
    private Integer templateId;

    /**
     * 邮件主题
     */
    @Size(max = 200, message = "邮件主题长度不能超过200个字符")
    private String emailSubject;

    /**
     * 邮件内容
     */
    private String emailBody;
}
