package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * 项目账户类型实体类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_accounttype")
public class ProjectAccountType  extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;
    /**
     * 账户类型ID
     */
    @TableField(value = "accounttype_id")
    private Integer accountTypeId;
    /**
     * 账户类型名称
     */
    @TableField(value = "type_id")
    private Integer typeId;
    /**
     * 账户类型名称
     */
    @TableField(value = "accounttype_name")
    private String accounttypeName;
}
