package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.Date;
/**
 *  工作流状态数据 workflow_state
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("workflow_state")
public class WorkflowState extends STBaseEntity{
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * State_id
     */
    @TableField(value = "state_id")
    private Integer stateId;

    /**
     * State_name
     */
    @TableField(value = "state_name")
    private String stateName;

    /**
     * state_option_id
     */
    @TableField(value = "state_option_id")
    private Integer stateOptionId;
}
