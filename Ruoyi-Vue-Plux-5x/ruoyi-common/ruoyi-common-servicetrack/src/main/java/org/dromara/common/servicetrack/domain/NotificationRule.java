package org.dromara.common.servicetrack.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * notification_rule表的直接映射类
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("notification_rule")
public class NotificationRule extends BaseEntity  {
    /**
     * key_id
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project_id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * rule_id
     */
    @TableField(value = "rule_id")
    private Integer ruleId;

    /**
     * object_id
     */
    @TableField(value = "object_id")
    private Integer objectId;

    /**
     * rule_name
     */
    @TableField(value = "rule_name")
    private String ruleName;

    /**
     * rule_type
     */
    @TableField(value = "rule_type")
    private Integer ruleType;

    /**
     * is_active
     */
    @TableField(value = "is_active")
    private Integer isActive;
}
