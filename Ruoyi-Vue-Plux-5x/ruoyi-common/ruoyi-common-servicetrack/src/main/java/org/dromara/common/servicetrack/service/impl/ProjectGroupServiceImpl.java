package org.dromara.common.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.domain.ProjectGroup;
import org.dromara.common.servicetrack.domain.bo.ProjectGroupBo;
import org.dromara.common.servicetrack.domain.vo.ProjectGroupVo;
import org.dromara.common.servicetrack.mapper.ProjectGroupMapper;
import org.dromara.common.servicetrack.mapper.ProjectGroupUserMapper;
import org.dromara.common.servicetrack.service.IProjectGroupService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 项目分组 服务层实现
 *
 * <AUTHOR> Fei
 */
@RequiredArgsConstructor
@Service
public class ProjectGroupServiceImpl implements IProjectGroupService {
    private final ProjectGroupMapper baseMapper;
    private final ProjectGroupUserMapper baseUserMapper;
    private final TableSequenceManager tableSequenceManager;

    @Override
    public List<ProjectGroupVo> selectGroupList(Integer projectId) {
        return baseMapper.selectGroupList(projectId);
    }

    /**
     * 查询项目分组
     */
    @Override
    public ProjectGroupVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询项目分组列表
     */
    @Override
    public TableDataInfo<ProjectGroupVo> queryPageList(Integer projectId, PageQuery pageQuery) {
        ProjectGroupBo bo = new ProjectGroupBo();
        bo.setProjectId(projectId);
        return TableDataInfo.<ProjectGroupVo>build((com.baomidou.mybatisplus.core.metadata.IPage<ProjectGroupVo>) baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(bo)));
    }

    private Wrapper<ProjectGroup> buildQueryWrapper(ProjectGroupBo bo) {
        QueryWrapper<ProjectGroup> wrapper = Wrappers.query();
        wrapper.eq(bo.getProjectId() != null, "project_id", bo.getProjectId());
        wrapper.eq(bo.getGroupId() != null, "group_id", bo.getGroupId());
        wrapper.like(StringUtils.isNotBlank(bo.getGroupName()), "group_name", bo.getGroupName());
        return wrapper;
    }

    /**
     * 新增项目分组
     */
    @Override
    public Integer insertByBo(ProjectGroupBo bo) {
        int groupId = tableSequenceManager.getNextSequence(SequenceTable.Project_Group, bo.getProjectId());
        bo.setGroupId(groupId);
        ProjectGroup add = MapstructUtils.convert(bo, ProjectGroup.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag ? add.getGroupId() : 0;
    }

    /**
     * 修改项目分组
     */
    @Override
    public Integer updateByBo(ProjectGroupBo bo) {
        ProjectGroup update = MapstructUtils.convert(bo, ProjectGroup.class);
        return baseMapper.updateById(update) > 0 ? update.getGroupId() : 0;
    }

    /**
     * 批量删除项目分组
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        var grouplist = baseMapper.selectList(Wrappers.<ProjectGroup>lambdaQuery().in(ProjectGroup::getId, ids));
        if( grouplist.isEmpty())
        {
            return false;
        }
        var groupIds = grouplist.stream().map(ProjectGroup::getGroupId).toList();
        var projectId = grouplist.stream().map(ProjectGroup::getProjectId).distinct().findFirst().orElse(0);;
        baseUserMapper.deleteGroupUserByGroupIds(projectId,groupIds);

        return baseMapper.deleteByIds(ids) > 0;
    }
}
