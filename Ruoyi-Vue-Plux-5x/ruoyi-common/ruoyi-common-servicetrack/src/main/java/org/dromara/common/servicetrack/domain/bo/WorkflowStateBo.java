package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.WorkflowState;

import java.util.Date;

/**
 * 工作流状态Bo
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WorkflowState.class)
public class WorkflowStateBo extends STBaseEntity{
    /**
     * ID
     */
    //@NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    @Min(value = 1, message = "projectId 必须大于 0")
    private Integer projectId;

    /**
     * State_id
     */
    private Integer stateId;

    /**
     * State_name
     */
    private String stateName;

    /**
     * state_option_id
     */
    private Integer stateOptionId;
}
