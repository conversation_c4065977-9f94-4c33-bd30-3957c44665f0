package org.dromara.common.servicetrack.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.List;

/**
 * 项目排序绑定对象Bo
 *
 * <AUTHOR> fei
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectSortBinderBo extends STBaseEntity {

    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 排序 Long ID
     */
    private List<Long> ids;
}
