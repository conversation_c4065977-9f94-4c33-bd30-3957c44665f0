package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ProjectGroupUser;
import org.dromara.common.servicetrack.domain.bo.ProjectGroupUserBo;
import org.dromara.common.servicetrack.domain.vo.ProjectGroupUserVo;

import java.util.List;

/**
 * 项目分组用户 数据层
 *
 * <AUTHOR>
 */
public interface ProjectGroupUserMapper extends BaseMapperPlus<ProjectGroupUser, ProjectGroupUserVo> {

    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ProjectGroupUser> buildWrapper(ProjectGroupUserBo bo) {
        LambdaQueryWrapper<ProjectGroupUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProjectId() != null, ProjectGroupUser::getProjectId, bo.getProjectId());
        lqw.eq(bo.getGroupId() != null, ProjectGroupUser::getGroupId, bo.getGroupId());
        lqw.eq(bo.getUserId() != null, ProjectGroupUser::getUserId, bo.getUserId());
        return lqw;
    }

    /**
     * 查询项目分组用户列表
     *
     * @param projectId 项目ID
     * @param groupId   分组ID
     * @return 项目分组用户列表
     */
    List<ProjectGroupUserVo> selectGroupUserList(@Param("projectId") Integer projectId, @Param("groupId") Integer groupId);

    /**
     * 批量删除项目分组用户
     *
     * @param projectId 项目ID
     * @param groupIds  分组ID
     */
    void deleteGroupUserByGroupIds(@Param("projectId") Integer projectId,@Param("groupIds") List<Integer> groupIds);
}
