package org.dromara.common.servicetrack.constant;


import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum eSTModuleType {
    NoneEnabled(0x00),
    EPEnabled(0x01),
    SPEnabled(0x02);

    private final int mask;

    eSTModuleType(int mask) {
        this.mask = mask;
    }

    public int getMask() {
        return mask;
    }

    // 组合多个类型
    public static int combine(eSTModuleType... types) {
        return Arrays.stream(types).mapToInt(t -> t.mask).reduce(0, (a, b) -> a | b);
    }

    // 检查是否包含某个类型
    public static boolean has(eSTModuleType type, int combinedMask) {
        return (combinedMask & type.mask) != 0;
    }

    // 获取所有启用的类型
    public static List<eSTModuleType> getAllEnabled(int combinedMask) {
        return Arrays.stream(values())
            .filter(t -> (combinedMask & t.mask) != 0)
            .collect(Collectors.toList());
    }

    // 检查是否同时包含EPEnabled和SPEnabled类型
    public static boolean isBothEPAndSPEnabled(int combinedMask) {
        int requiredMask = EPEnabled.mask | SPEnabled.mask;
        return (combinedMask & requiredMask) == requiredMask;
    }
}

