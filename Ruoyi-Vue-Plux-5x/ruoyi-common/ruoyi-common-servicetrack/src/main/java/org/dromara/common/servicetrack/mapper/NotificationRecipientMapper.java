package org.dromara.common.servicetrack.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.servicetrack.domain.NotificationRecipient;
import org.dromara.common.servicetrack.domain.bo.NotificationRecipientBo;
import org.dromara.common.servicetrack.domain.vo.NotificationRecipientVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 通知接收人管理 数据层
 *
 * <AUTHOR>
 */
public interface NotificationRecipientMapper extends BaseMapperPlus<NotificationRecipient, NotificationRecipientVo> {
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<NotificationRecipient> buildWrapper(NotificationRecipientBo bo) {
        LambdaQueryWrapper<NotificationRecipient> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getKeyId() != null, NotificationRecipient::getKeyId, bo.getKeyId());
        lqw.eq(bo.getProjectId() != null, NotificationRecipient::getProjectId, bo.getProjectId());
        lqw.eq(bo.getRuleId() != null, NotificationRecipient::getRuleId, bo.getRuleId());
        lqw.eq(bo.getUserType() != null, NotificationRecipient::getUserType, bo.getUserType());
        lqw.eq(bo.getUserId() != null, NotificationRecipient::getUserId, bo.getUserId());
        return lqw;
    }

    /**
     * 根据项目ID和规则ID查询接收人列表
     *
     * @param projectId 项目ID
     * @param ruleId    规则ID
     * @return 接收人列表
     */
    List<NotificationRecipientVo> selectByProjectAndRule(@Param("projectId") Integer projectId, @Param("ruleId") Integer ruleId);

    /**
     * 根据项目ID查询所有接收人
     *
     * @param projectId 项目ID
     * @return 接收人列表
     */
    List<NotificationRecipientVo> selectByProjectId(@Param("projectId") Integer projectId);

    /**
     * 根据用户类型查询接收人列表
     *
     * @param projectId 项目ID
     * @param userType  用户类型
     * @return 接收人列表
     */
    List<NotificationRecipientVo> selectByUserType(@Param("projectId") Integer projectId, @Param("userType") Integer userType);

    /**
     * 根据用户ID查询接收人列表
     *
     * @param projectId 项目ID
     * @param userId    用户ID
     * @return 接收人列表
     */
    List<NotificationRecipientVo> selectByUserId(@Param("projectId") Integer projectId, @Param("userId") Integer userId);
}
