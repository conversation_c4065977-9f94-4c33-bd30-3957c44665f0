package org.dromara.common.servicetrack.sequence;

import java.util.HashMap;
import java.util.Map;

/**
 * 表定义 - 管理表的元数据信息
 *
 * <AUTHOR>
 */
public class TableDefinition {

    private static final Map<SequenceTable, TableDefinition> TABLE_DEFINITIONS = new HashMap<>();

    static {
        // 初始化表定义
        register(SequenceTable.USER, "sys_user", "", "external_user_id", "用户表");
        register(SequenceTable.PROJECT, "project_info", "", "project_id", "项目表");
        register(SequenceTable.Item_Info, "item_info", "project_id", "item_id", "条目表");
        register(SequenceTable.Item_Changelog, "item_changelog", "project_id", "changelog_id", "条目变更日志表");
        register(SequenceTable.Item_History, "item_history", "project_id", "seq_no", "条目历史记录表");
        register(SequenceTable.Item_Attachment, "item_attachment", "project_id", "attachment_id", "条目附件表");
        register(SequenceTable.Project_AccountType, "project_accounttype", "project_id", "accounttype_id", "项目账户表");
        register(SequenceTable.Project_Group, "project_group", "project_id", "group_id", "项目分组表");
        register(SequenceTable.Project_Field_Custom, "project_field_custom", "project_id", "field_id", "项目自定义字段表");
        register(SequenceTable.Project_Item_Type, "project_itemtype", "project_id", "type_id", "项目条目类型表");
        register(SequenceTable.Project_Page, "project_page", "project_id", "page_id", "项目自定义页面表");
        register(SequenceTable.UserInfo_Changelog, "user_info_changelog", "project_id", "changelog_id", "用户信息变更日志表");
    }

    private final String tableName;      // 表名
    private final String parentIdColumn; // 父ID列名
    private final String sequenceColumn; // 序列号列名
    private final String description;    // 表描述

    private TableDefinition(String tableName, String parentIdColumn, String sequenceColumn, String description) {
        this.tableName = tableName;
        this.parentIdColumn = parentIdColumn;
        this.sequenceColumn = sequenceColumn;
        this.description = description;
    }

    /**
     * 注册表定义
     */
    private static void register(SequenceTable table, String tableName, String parentIdColumn, String sequenceColumn, String description) {
        TABLE_DEFINITIONS.put(table, new TableDefinition(tableName, parentIdColumn, sequenceColumn, description));
    }

    /**
     * 获取表定义
     */
    public static TableDefinition get(SequenceTable table) {
        TableDefinition definition = TABLE_DEFINITIONS.get(table);
        if (definition == null) {
            throw new IllegalArgumentException("No definition found for table: " + table);
        }
        return definition;
    }

    public String getTableName() {
        return tableName;
    }

    public String getParentIdColumn() {
        return parentIdColumn;
    }

    public String getSequenceColumn() {
        return sequenceColumn;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 是否支持父ID
     */
    public boolean supportsParentId() {
        return !parentIdColumn.isEmpty();
    }
}
