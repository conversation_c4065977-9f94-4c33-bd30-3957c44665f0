package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.NotificationRecipient;

import java.io.Serial;
import java.io.Serializable;

/**
 * 通知接收人视图对象 notification_recipient
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = NotificationRecipient.class)
public class NotificationRecipientVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long keyId;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 规则ID
     */
    private Integer ruleId;

    /**
     * 用户类型
     */
    private Integer userType;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名称（扩展字段，用于显示）
     */
    private String userName;

    /**
     * 用户昵称（扩展字段，用于显示）
     */
    private String nickName;
}
