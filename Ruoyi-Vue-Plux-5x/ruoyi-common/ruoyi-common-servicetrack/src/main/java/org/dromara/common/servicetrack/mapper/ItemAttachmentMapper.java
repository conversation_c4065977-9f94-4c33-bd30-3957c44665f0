package org.dromara.common.servicetrack.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.servicetrack.domain.ItemAttachment;
import org.dromara.common.servicetrack.domain.ProjectInfo;
import org.dromara.common.servicetrack.domain.bo.ItemAttachmentBo;
import org.dromara.common.servicetrack.domain.bo.ProjectInfoBo;
import org.dromara.common.servicetrack.domain.vo.ItemAttachmentVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jodd.introspector.Mapper;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.Collection;
import java.util.List;


/**
 * item 附件管理 数据层
 *
 * <AUTHOR> fei
 */
public interface ItemAttachmentMapper extends BaseMapperPlus<ItemAttachment, ItemAttachmentVo>{
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ItemAttachment> buildWrapper(ItemAttachmentBo bo) {
        LambdaQueryWrapper<ItemAttachment> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, ItemAttachment::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, ItemAttachment::getProjectId, bo.getProjectId());
        lqw.like(StringUtils.isNotBlank(bo.getAttachmentName()), ItemAttachment::getAttachmentName, bo.getAttachmentName());
        lqw.eq(bo.getItemId() != null, ItemAttachment::getItemId, bo.getItemId());
        lqw.eq(bo.getOssId() != null, ItemAttachment::getOssId, bo.getOssId());
        lqw.eq(bo.getTypeId() != null, ItemAttachment::getTypeId, bo.getTypeId());
        lqw.eq(bo.getStateId() != null, ItemAttachment::getStateId, bo.getStateId());
        return lqw;
    }

    /**
     * 查询item附件列表
     *
     * @param projectId 项目id
     * @param itemId    item id
     * @return item附件集合
     */
    List<ItemAttachmentVo> selectItemAttachmentList(@Param("projectId") Integer projectId, @Param("itemId") Integer itemId);

    List<Long> selectItemAttachmentIdsByOssIds(@Param("ossIds") Collection<?> ossIdsList);
}
