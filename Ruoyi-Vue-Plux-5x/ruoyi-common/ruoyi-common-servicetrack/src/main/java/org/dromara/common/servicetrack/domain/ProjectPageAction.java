package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_page_action")
public class ProjectPageAction extends STBaseEntity {
    @TableId(value = "key_id")
    private Long id;

    @TableField(value = "project_id")
    private Integer projectId;

    @TableField(value = "itemtype_id")
    private Integer itemTypeId;

    @TableField(value = "page_id")
    private Integer pageId;

    @TableField(value = "action_id")
    private Integer actionId;

    @TableField(value = "page_order")
    private Integer pageOrder;
}
