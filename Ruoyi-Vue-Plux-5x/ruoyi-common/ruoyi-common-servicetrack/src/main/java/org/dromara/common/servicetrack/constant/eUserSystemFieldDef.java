package org.dromara.common.servicetrack.constant;

import java.util.Arrays;
import java.util.List;

public enum eUserSystemFieldDef implements IBaseFieldEnum, IValueEnum{
    UserId(1),
    <PERSON>r<PERSON><PERSON>(2),
    <PERSON><PERSON><PERSON>(3),
    <PERSON><PERSON>(4),
    <PERSON>(5),
    <PERSON>(6),
    <PERSON><PERSON><PERSON>(7),
    <PERSON><PERSON><PERSON>(8),
    Created<PERSON>y(9),
    JobTitle(30),
    JobDuty(31),
    SupportTeam(32),
    PrimarySupport(33),
    SecondarySupport(34),
    IsVip(35);

    private final int value;
    /*
     *构造函数在定义常量时自动调用
     */
    eUserSystemFieldDef(int value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return value;
    }
    public static eUserSystemFieldDef from(Integer value) {
        return IValueEnum.valueOf(eUserSystemFieldDef.class, value);
    }
    public static List<Integer> getSystemFieldIds(){
        return IValueEnum.getSystemFieldIds(eUserSystemFieldDef.class);
    }
    public static boolean IsSystemField(Integer fieldId){
        return IValueEnum.IsSystemField(eUserSystemFieldDef.class, fieldId);
    }
    public static boolean IsSTUserInfoSystemField(Integer fieldId){
        return  fieldId >= JobTitle.value && fieldId < stConstant.DEFAULT_CUSTOM_FIELD_ID;
    }
    @Override
    public String getName(){
        // TODO Auto-generated method stub
        return switch ( this.value){
            case 1 -> "用户ID";//"UserId";
            case 2 -> "登录名";//"UserName";
            case 3 -> "用户名";//"NickName";
            case 4 -> "邮箱";//"Email";
            case 5 -> "电话号码";//"Phone";
            case 6 -> "状态";//"Status";
            case 7 -> "部门";//"Depart";
            case 8 -> "创建日期";//"CreatedTime";
            case 9 -> "创建者";//"CreatedBy";
            case 30 -> "职位";//"JobTitle";
            case 31 -> "职务";//"JobDuty";
            case 32 -> "支持团队";//"SupportTeam";
            case 33 -> "首要支持人";//"PrimarySupport";
            case 34 -> "次要支持人";//"SecondarySupport";
            case 35 -> "是否VIP";//"IsVip";
            default -> "";
        };
    }
    @Override
    public String getDefaultName() {
        return this.name();
    }
    @Override
    public Integer getFieldType(){
        // TODO Auto-generated method stub
        return switch ( this.value){
            case 1 ->  1; //"UserId";
            case 2 ->  1;//"UserName";
            case 3 ->  1; //"NickName";
            case 4 ->  1;//"Email";
            case 5 ->  1;// "Phone";
            case 6 ->  3;//"Status";
            case 7 ->  3;//"Depart";
            case 8 ->  5;//"CreatedTime";
            case 9 ->  3;//"CreatedBy";
            case 30 -> 10;//"JobTitle";
            case 31 -> 10;//"JobDuty";
            case 32 -> 3;//"SupportTeam";
            case 33 -> 3;//"PrimarySupport";
            case 34 -> 3;//"SecondarySupport";
            case 35 -> 3;//"IsVip";
            default -> 0;
        };
    }
}
