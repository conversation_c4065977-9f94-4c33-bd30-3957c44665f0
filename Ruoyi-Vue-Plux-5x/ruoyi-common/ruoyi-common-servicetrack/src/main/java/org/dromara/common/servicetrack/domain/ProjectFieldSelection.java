package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.Date;
/**
 *  字段选择数据 Project_Field_Selection
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_field_selection")
public class ProjectFieldSelection extends STBaseEntity {

    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * field id
     */
    @TableField(value = "field_id")
    private Integer fieldId;

    /**
     * choice id
     */
    @TableField(value = "choice_id")
    private Integer choiceId;

    /**
     * choice name
     */
    @TableField(value = "choice_name")
    private String choiceName;

    /**
     * choice order
     */
    @TableField(value = "choice_order")
    private Integer choiceOrder;
}
