package org.dromara.common.servicetrack.domain.bo;

import java.io.Serializable;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ItemAttachment;
import org.dromara.common.servicetrack.domain.NotificationRule;

/**
 * notification_rule表的bo类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = NotificationRule.class)
public class NotificationRuleBo extends STBaseEntity {


    /**
     * key_id
     */
    private Long id;

    /**
     * project_id
     */
    private Integer projectId;

    /**
     * rule_id
     */
    private Integer ruleId;

    /**
     * object_id
     */
    private Integer objectId;

    /**
     * rule_name
     */
    private String ruleName;

    /**
     * rule_type
     */
    private Integer ruleType;

    /**
     * is_active
     */
    private Integer isActive;
}
