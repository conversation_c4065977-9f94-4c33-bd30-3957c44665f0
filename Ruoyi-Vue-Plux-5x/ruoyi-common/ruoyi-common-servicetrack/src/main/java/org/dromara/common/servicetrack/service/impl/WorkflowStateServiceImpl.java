package org.dromara.common.servicetrack.service.impl;

import lombok.RequiredArgsConstructor;
import org.dromara.common.servicetrack.domain.bo.WorkflowStateBo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldSelectionVo;
import org.dromara.common.servicetrack.domain.vo.WorkflowStateVo;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.mapper.WorkflowStateMapper;
import org.dromara.common.servicetrack.service.IWorkflowStateService;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
/**
 * 工作流状态管理 服务层实现
 *
 * <AUTHOR> <PERSON>
 */
@RequiredArgsConstructor
@Service
public class WorkflowStateServiceImpl implements IWorkflowStateService {

    private final WorkflowStateMapper workflowStateMapper;
    @Override
    public List<WorkflowStateVo> selectStateList(WorkflowStateBo bo, boolean fromCache) {
        if(fromCache)
        {
            return ProjectManager.getInstance(bo.getProjectId()).getWorkflowStates();
        }

        return workflowStateMapper.selectVoList(workflowStateMapper.buildWrapper(bo));
    }
}
