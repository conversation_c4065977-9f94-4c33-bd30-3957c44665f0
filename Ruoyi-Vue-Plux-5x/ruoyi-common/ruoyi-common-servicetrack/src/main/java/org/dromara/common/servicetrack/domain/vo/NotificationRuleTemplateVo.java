package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.NotificationRuleTemplate;

import java.io.Serial;
import java.io.Serializable;

/**
 * 通知规则模板视图对象 notification_rule_template
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = NotificationRuleTemplate.class)
public class NotificationRuleTemplateVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long keyId;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 规则ID
     */
    private Integer ruleId;

    /**
     * 模板类型
     */
    private Integer templateType;

    /**
     * 模板ID
     */
    private Integer templateId;
}
