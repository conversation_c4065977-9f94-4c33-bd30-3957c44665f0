package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.servicetrack.domain.ProjectFieldSelection;
import org.dromara.common.servicetrack.domain.WorkflowState;

import java.io.Serializable;

/**
 * 工作流状态VO
 *
 * <AUTHOR> fei
 */

@Data
@AutoMapper(target = WorkflowState.class)
public class WorkflowStateVo implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * ProjectID
     */
    private Integer projectId;

    /**
     * State_id
     */
    private Integer stateId;

    /**
     * State_name
     */
    private String stateName;

    /**
     * state_option_id
     */
    private Integer stateOptionId;
}
