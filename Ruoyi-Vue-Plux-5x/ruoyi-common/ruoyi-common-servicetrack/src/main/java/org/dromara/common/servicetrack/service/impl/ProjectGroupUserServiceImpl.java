package org.dromara.common.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.ProjectGroupUser;
import org.dromara.common.servicetrack.domain.bo.ProjectGroupUserBo;
import org.dromara.common.servicetrack.domain.vo.ProjectGroupUserVo;
import org.dromara.common.servicetrack.mapper.ProjectGroupUserMapper;
import org.dromara.common.servicetrack.service.IProjectGroupUserService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 项目分组用户 服务层实现
 *
 * <AUTHOR>
@RequiredArgsConstructor
@Service
public class ProjectGroupUserServiceImpl implements IProjectGroupUserService {
    private final ProjectGroupUserMapper baseMapper;

    @Override
    public List<ProjectGroupUserVo> selectGroupUserList(Integer projectId, Integer groupId) {
        return baseMapper.selectGroupUserList(projectId, groupId);
    }

    /**
     * 查询项目分组用户
     */
    @Override
    public ProjectGroupUserVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询项目分组用户列表
     */
    @Override
    public TableDataInfo<ProjectGroupUserVo> queryPageList(Integer projectId, PageQuery pageQuery) {
        ProjectGroupUserBo bo = new ProjectGroupUserBo();
        bo.setProjectId(projectId);
        return TableDataInfo.<ProjectGroupUserVo>build((com.baomidou.mybatisplus.core.metadata.IPage<ProjectGroupUserVo>) baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(bo)));
    }

    private Wrapper<ProjectGroupUser> buildQueryWrapper(ProjectGroupUserBo bo) {
        QueryWrapper<ProjectGroupUser> wrapper = Wrappers.query();
        wrapper.eq(bo.getProjectId() != null, "project_id", bo.getProjectId());
        wrapper.eq(bo.getGroupId() != null, "group_id", bo.getGroupId());
        wrapper.eq(bo.getUserId() != null, "user_id", bo.getUserId());
        return wrapper;
    }

    /**
     * 新增项目分组用户
     */
    @Override
    public Boolean insertByBo(ProjectGroupUserBo bo) {
        return updateProjectGroupUser(bo) ;
    }
    private boolean updateProjectGroupUser(ProjectGroupUserBo bo){
        if( bo.getUserIds() == null ||  bo.getUserIds().isEmpty()){
            return false;
        }
        var projectId = bo.getProjectId();
        var groupId = bo.getGroupId();

        var existedGroupUsers = baseMapper.selectGroupUserList(projectId, groupId);
        List<ProjectGroupUser> newProjectGroupUsers = new ArrayList<>();
        for(var userId:bo.getUserIds()){
            boolean isExisted = false;
            for(var groupUser:existedGroupUsers){
                if(groupUser.getUserId().equals(userId)){
                    isExisted = true;
                    break;
                }
            }
            if(!isExisted){
                ProjectGroupUser addUser = new ProjectGroupUser();
                addUser.setProjectId(projectId);
                addUser.setGroupId(groupId);
                addUser.setUserId(userId);
                newProjectGroupUsers.add(addUser);
            }
        }
        boolean flag = false;
        if(!newProjectGroupUsers.isEmpty()){
            flag  = baseMapper.insertBatch(newProjectGroupUsers);
        }

        return flag;
    }
    /**
     * 修改项目分组用户
     */
    @Override
    public Boolean updateByBo(ProjectGroupUserBo bo) {
        return updateProjectGroupUser(bo) ;
    }

    /**
     * 批量删除项目分组用户
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
