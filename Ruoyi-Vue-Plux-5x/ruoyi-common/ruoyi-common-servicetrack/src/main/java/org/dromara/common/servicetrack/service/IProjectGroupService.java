package org.dromara.common.servicetrack.service;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectGroupBo;
import org.dromara.common.servicetrack.domain.vo.ProjectGroupVo;

import java.util.Collection;
import java.util.List;

/**
 * 项目分组 服务层
 *
 * <AUTHOR>
 */
public interface IProjectGroupService {

    /**
     * 查询项目分组列表
     *
     * @param projectId 项目ID
     * @return 项目分组列表
     */
    List<ProjectGroupVo> selectGroupList(Integer projectId);
    /**
     * 查询项目分组
     */
    ProjectGroupVo queryById(Long id);
    /**
     * 查询项目分组列表
     */
    TableDataInfo<ProjectGroupVo> queryPageList(Integer projectId, PageQuery pageQuery);
    /**
     * 新增项目分组列表
     */
    Integer insertByBo(ProjectGroupBo bo);
    /**
     * 修改项目分组列表
     */
    Integer updateByBo(ProjectGroupBo bo);
    /**
     * 校验并批量删除项目分组信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
