package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ProjectFieldCustom;

/**
 * 项目自定义字段业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectFieldCustom.class)
public class ProjectFieldCustomBo extends STBaseEntity {
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 字段ID
     */
    @NotNull(message = "字段ID不能为空")
    private Integer fieldId;

    /**
     * 字段名称
     */
    @NotNull(message = "字段名称不能为空")
    @Xss(message = "字段名称不能包含脚本字符")
    @NotBlank(message = "字段名称不能为空")
    @Size(min = 0, max = 200, message = "字段名称的长度不能超过{max}")
    private String fieldName;

    /**
     * 模块ID
     */
    private Integer moduleId;

    /**
     * 字段类型
     */
    private Integer fieldType;
}
