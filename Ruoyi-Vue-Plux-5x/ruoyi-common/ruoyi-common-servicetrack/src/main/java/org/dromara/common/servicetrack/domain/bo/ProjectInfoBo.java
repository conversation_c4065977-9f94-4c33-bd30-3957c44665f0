package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ProjectInfo;

import java.util.Date;

/**
 * 事件业务对象 project_info
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectInfo.class)
public class ProjectInfoBo extends STBaseEntity{
    /**
     * ID
     */
    //@NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
     @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 项目类型
     */
    private  Integer projectType;

    /**
     * 项目名称
     */
    @NotNull(message = "项目名称不能为空")
    private String projectName;

    /**
     * 项目关键字
     */
    private String projectKey;

    /**
     * 是否激活
     */
    private Integer isActive;

    /**
     * base project id
     */
    private Integer baseProjectId;
}
