package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectFieldCustom;

import java.io.Serial;
import java.io.Serializable;

/**
 * 项目自定义字段视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = ProjectFieldCustom.class)
public class ProjectFieldCustomVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 字段ID
     */
    private Integer fieldId;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 模块ID
     */
    private Integer moduleId;

    /**
     * 字段类型
     */
    private Integer fieldType;
}
