package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.servicetrack.domain.WorkflowTransition;

import java.io.Serializable;

/**
 * 工作流转换VO
 *
 * <AUTHOR> fei
 */

@Data
@AutoMapper(target = WorkflowTransition.class)
public class WorkflowTransitionVo implements Serializable{
    /**
     * ID
     */
    private Long id;

    /**
     * ProjectID
     */
    private Integer projectId;

    /**
     * Transition_id
     */
    private Integer transitionId;

    /**
     * Transition_name
     */
    private String transitionName;

   /**
     * From_state_id
     */
    private Integer fromStateId;

    /**
     * To_state_id
     */
    private Integer toStateId;

    /**
     * Display_order
     */
    private Integer displayOrder;
}
