package org.dromara.common.servicetrack.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectItemTypeBo;
import org.dromara.common.servicetrack.domain.vo.ProjectItemTypeVo;

import java.util.Collection;
import java.util.List;

public interface IProjectItemTypeService {
    /**
     * 查询项目类型列表
     *
     * @return 项目类型列表
     */
    ProjectItemTypeVo queryById(Long keyId);
    /**
     * 查询项目类型列表
     *
     * @return 项目类型列表
     */
    TableDataInfo<ProjectItemTypeVo> queryPageList(Integer projectId, PageQuery pageQuery);

    /**
     * 查询项目类型列表
     *
     * @return 项目类型列表
     */
    List<ProjectItemTypeVo> selectItemTypeList(Integer projectId);
    /**
     * 新增项目类型列表
     *
     * @return 项目类型Id
     */
    Integer insertByBo(ProjectItemTypeBo bo);
    /**
     * 修改项目类型
     *
     * @return 项目类型Id
     */
    Integer updateByBo(ProjectItemTypeBo bo);
    /**
        * 删除项目类型
        *
        * @param keyIds 主键串
     */
    Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid);
}
