package org.dromara.common.servicetrack.sequence.impl;

import org.dromara.common.servicetrack.sequence.SequenceRepository;
import org.dromara.common.servicetrack.sequence.mapper.TableSequenceMapper;

import java.util.List;
import java.util.Map;

/**
 * 序列号数据库访问实现类
 *
 * <AUTHOR>
 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class SequenceRepositoryImpl implements SequenceRepository {

    private final TableSequenceMapper tableSequenceMapper;

    @Autowired
    public SequenceRepositoryImpl(TableSequenceMapper tableSequenceMapper) {
        this.tableSequenceMapper = tableSequenceMapper;
    }

    @Override
    public List<Map<String, Object>> getMaxSequences(List<Map<String, String>> tableInfos) {
        return tableSequenceMapper.getMaxSequences(tableInfos);
    }

    @Override
    public int getMaxSequenceByParent(String tableName, String sequenceColumn, String parentIdColumn, int parentId) {
        return tableSequenceMapper.getMaxSequenceByParent(tableName, sequenceColumn, parentIdColumn, parentId);
    }

    @Override
    public List<Map<String, Object>> getAllParentMaxSequences(List<Map<String, String>> tableInfos) {
        return tableSequenceMapper.getAllParentMaxSequences(tableInfos);
    }
}
