package org.dromara.common.servicetrack.constant;

public enum eFieldTypeDef implements IValueEnum {
    ShortText(1),
    PlainText(2),
    Dropdown(3),
    MultipleSelection(4),
    Date(5),
    RichText(6),
    CheckBox(7),
    RadioBox(8),
    StaticBox(9),
    ComboBox(10);

    private final int value;
    /*
     *构造函数在定义常量时自动调用
     */
    eFieldTypeDef(int value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return value;
    }

    public static eFieldTypeDef from(Integer value) {
        return IValueEnum.valueOf(eFieldTypeDef.class, value);
    }
}
