package org.dromara.common.servicetrack.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 工作流转换状态VO
 *
 * <AUTHOR> fei
 */

@Data
@NoArgsConstructor
public class WorkflowTransitionStateVo implements Serializable{
    /**
     * From_state_id
     */
    private Integer fromStateId;

    /**
     * From_state_name
     */
    private String fromStateName;

    /**
     * Transition next states
     */
    private List<WorkflowTransitionNextStateVo> nextStates;
}

