package org.dromara.common.servicetrack.domain;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.Date;
/**
 *  工作流转变数据 workflow_transition
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("workflow_transition")
public class WorkflowTransition extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * transition_id
     */
    @TableField(value = "transition_id")
    private Integer transitionId;

    /**
     * transition_name
     */
    @TableField(value = "transition_name")
    private String transitionName;

    /**
     * from_state_id
     */
    @TableField(value = "from_state_id")
    private Integer fromStateId;

    /**
     * to_state_id
     */
    @TableField(value = "to_state_id")
    private Integer toStateId;

    /**
     * display_order
     */
    @TableField(value = "display_order")
    private Integer displayOrder;
}
