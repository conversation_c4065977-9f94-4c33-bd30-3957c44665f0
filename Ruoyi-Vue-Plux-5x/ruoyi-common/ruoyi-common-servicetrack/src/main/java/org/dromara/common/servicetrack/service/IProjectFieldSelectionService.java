package org.dromara.common.servicetrack.service;

import org.dromara.common.servicetrack.domain.ProjectFieldSelection;
import org.dromara.common.servicetrack.domain.bo.*;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldSelectionVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 字段选项管理 服务层
 *
 * <AUTHOR>
 * */
public interface IProjectFieldSelectionService {
    /**
     * 根据条件查询字段选项列表
     *
     * @param field 字段选项信息
     * @return 字段选项集合信息
     */
    List<ProjectFieldSelectionVo> selectFieldSelctionList(ProjectFieldSelectionBo field, boolean fromCache);

    /**
     * 根据字段id查询ChoiceIds
     *
     * @param field 字段信息
     * @return 字段信息集合信息
     */
    Map<Integer, List<ProjectFieldSelectionVo>> getChoiceIdsByFieldId(FieldChoicesBo field);
    /**
     * 新增字段选项信息
     *
     * @param bo 字段选项信息
     * @return 结果
     */
    Integer insertByBo(ProjectFieldSelectionBo bo);
    /**
     * 修改字段选项
     *
     */
    Integer updateByBo(ProjectFieldSelectionBo bo);

    /**
     * 批量修改字段选项
     *
     */
    Boolean updateBatchByBo(ProjectFieldSelectionBatchBo bo);
    /**
     * 删除字段选项
     *
     * @param keyIds 主键串
     */
    Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid);

    /**
     * 根据ID查询字段选项
     */
    Boolean sortSelection(ProjectSortBinderBo bo);
}
