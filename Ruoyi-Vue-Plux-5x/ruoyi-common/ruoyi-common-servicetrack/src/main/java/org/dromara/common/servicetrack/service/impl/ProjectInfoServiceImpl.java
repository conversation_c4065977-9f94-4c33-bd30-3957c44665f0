package org.dromara.common.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.servicetrack.constant.eSTModuleType;
import org.dromara.common.servicetrack.domain.ProjectMember;
import org.dromara.common.servicetrack.domain.vo.ProjectMemberVo;
import org.dromara.common.servicetrack.mapper.ProjectMemberMapper;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.domain.ProjectInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectInfoBo;
import org.dromara.common.servicetrack.domain.vo.ProjectInfoVo;
import org.dromara.common.servicetrack.mapper.ProjectInfoMapper;
import org.dromara.common.servicetrack.service.IProjectInfoService;
import org.dromara.common.servicetrack.mapper.ProjectAccountTypeMapper;
import org.dromara.common.servicetrack.service.IProjectAccountTypeService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 项目管理 服务层实现
 *
 * <AUTHOR> Fei
 */
@RequiredArgsConstructor
@Service
public class ProjectInfoServiceImpl implements IProjectInfoService {
    private final ProjectInfoMapper baseMapper;
    private final TableSequenceManager tableSequenceManager;
    // 新增 ProjectAccountTypeMapper 和 IProjectAccountTypeService
    private final ProjectAccountTypeMapper projectAccountTypeMapper;
    private final IProjectAccountTypeService projectAccountTypeService;
    private final ProjectMemberMapper projectMemberMapper;

    /**
     * 查询项目
     */
    @Override
    public ProjectInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }
    /**
     * 查询项目列表
     */
    @Override
    public TableDataInfo<ProjectInfoVo> queryPageList(PageQuery pageQuery) {
        ProjectInfoBo bo = new ProjectInfoBo();
        //bo.setProjectType(eProjectType.BASE_PROJECT.getValue());//base project type
        return TableDataInfo.<ProjectInfoVo>build((com.baomidou.mybatisplus.core.metadata.IPage<ProjectInfoVo>) baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(bo)));
    }
    private Wrapper<ProjectInfo> buildQueryWrapper(ProjectInfoBo bo){
        QueryWrapper<ProjectInfo> wrapper = Wrappers.query();
        wrapper.ne(bo.getProjectType() != null, "project_type", bo.getProjectType());
        return wrapper;
    }
    /**
     * 查询项目列表
     */
    @Override
    public List<ProjectInfoVo> queryList(ProjectInfoBo bo) {
        return baseMapper.selectVoList(this.buildQueryWrapper(bo));
    }

    @Override
    public List<ProjectInfoVo> queryListByUser() {
        var logUser = LoginHelper.getLoginUser();
        if( logUser == null)
            throw new ServiceException("登录用户不存在!");
        var userId = logUser.getExternalUserId();
        var stUserType = logUser.getStUserType();
        var isSWPUser = (stUserType == eSTModuleType.SPEnabled.getMask() || eSTModuleType.isBothEPAndSPEnabled(stUserType));
        var isSTAdmin = LoginHelper.isSTAdmin(logUser.getUserId());
        if(isSTAdmin)
            isSWPUser = false;
        var pmUserTypeId =  isSWPUser ? 1 : 2;
        var projectMembers = projectMemberMapper.selectVoList(new QueryWrapper<ProjectMember>().eq("user_id",userId).eq("user_type",pmUserTypeId));
        var isAvailablePMs = true;
        if( projectMembers == null || projectMembers.isEmpty())
        {
            isAvailablePMs = false;
            if( isSWPUser )
                return List.of();
        }
        var projectIds = isAvailablePMs ? projectMembers.stream().map(ProjectMemberVo::getProjectId).toList() : null;
        var bo = new ProjectInfoBo();
        //if current user is EWP, and he does not belong to any project member, just get all projects
        var allProjectList = (projectIds != null && !projectIds.isEmpty()) ? baseMapper.selectVoList(new QueryWrapper<ProjectInfo>().in("project_id",projectIds)):
            baseMapper.selectVoList(new QueryWrapper<ProjectInfo>().ne("project_type",10));
        var projectList = (projectIds != null && !projectIds.isEmpty()) ?  allProjectList.stream().filter(p -> projectIds.contains(p.getProjectId())).toList():allProjectList;
        //get base project list
        var baseProjectList = projectList.stream().map(ProjectInfoVo::getBaseProjectId).toList().stream().distinct().toList();
        List<ProjectInfoVo> availedProjectList = new ArrayList<>();
        for (var baseProjectId : baseProjectList) {
            var baseProject = selectProjectInfoById(baseProjectId);
            if( baseProject != null)
            {
                availedProjectList.add(baseProject);
                baseProject.setChildren(projectList.stream().filter(p -> p.getBaseProjectId().equals(baseProjectId)).toList());
            }
        }
        return availedProjectList;
    }

    /**
     * 根据项目id查询项目信息
     */
    @Override
    public ProjectInfoVo selectProjectInfoById(Integer projectId)    {
        ProjectInfoBo bo = new ProjectInfoBo();
        bo.setProjectId(projectId);
        var listProjects =  baseMapper.selectVoList( baseMapper.buildWrapper(bo));
        if(!listProjects.isEmpty())
        {
            return listProjects.get(0);
        }
        return  null;
    }
    /**
     * 新增项目
     */
    @Override
    public Integer insertByBo(ProjectInfoBo bo) {
        int projectId  = tableSequenceManager.getNextSequence(SequenceTable.PROJECT);
        bo.setProjectId(projectId);
        ProjectInfo add = MapstructUtils.convert(bo, ProjectInfo.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag ? add.getProjectId() : 0;
    }

    /**
     * 修改项目
     */
    @Override
    public Integer updateByBo(ProjectInfoBo bo) {
        ProjectInfo update = MapstructUtils.convert(bo, ProjectInfo.class);
        return baseMapper.updateById(update) > 0 ? update.getProjectId() : 0;
    }

    /**
     * 批量删除项目
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public String getEmailSetting(Integer projectId, Integer settingId) {
        return baseMapper.getEmailSetting(projectId, settingId);
    }
}
