package org.dromara.common.servicetrack.service;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectAccountTypeBo;
import org.dromara.common.servicetrack.domain.bo.ProjectMemberBinderBo;
import org.dromara.common.servicetrack.domain.bo.ProjectMemberBo;
import org.dromara.common.servicetrack.domain.vo.ProjectAccountTypeVo;
import org.dromara.common.servicetrack.domain.vo.ProjectMemberVo;
import org.dromara.common.servicetrack.domain.vo.SysUserSimpleInfoVo;

import java.util.Collection;
import java.util.List;

public interface IProjectMemberService {
    /**
     * 查询项目成员列表
     */
    List<ProjectMemberVo> selectMemberList(Integer projectId);

    /**
     * 获得所有用户列表
     */
    List<SysUserSimpleInfoVo> selectUserList();
    /**
     * 查询项目成员列表
     */
    TableDataInfo<ProjectMemberVo> queryPageList(Integer projectId, PageQuery pageQuery);
    /**
     * 新增项目成员
     */
    Boolean insertByBo(ProjectMemberBinderBo bo);

    /**
     * 修改项目成员
     */
    Boolean updateByBo(ProjectMemberBinderBo bo);

    /**
     * 校验并批量删除项目项目成员信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据部门ID查询部门名称
     */
    String getDeptNameById(Long deptId);
}
