package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ProjectFieldSelection;

import java.util.Date;

/**
 * 项目字段选择项对象 project_field_selection
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectFieldSelection.class)
public class ProjectFieldSelectionBo extends STBaseEntity {
    /**
     * ID
     */
    //@NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 字段ID
     */
    @NotNull(message = "字段ID不能为空")
    private Integer fieldId;

    /**
     * 选项ID
     */
    @NotNull(message = "选项ID不能为空")
    private Integer choiceId;

    /**
     * 选项名称
     */
    @Xss(message = "选项名称不能包含脚本字符")
    @NotBlank(message = "选项名称不能为空")
    @Size(min = 1, max = 200, message = "选项名称长度不能超过{max}个字符")
    private String choiceName;

    /**
     * 选项顺序
     */
    @NotNull(message = "选项顺序不能为空")
    private Integer choiceOrder;
}

