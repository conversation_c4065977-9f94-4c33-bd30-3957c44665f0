package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
/**
 * item附件对象 item_attachment
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("item_attachment")
public class ItemAttachment extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * item id
     */
    @TableField(value = "item_id")
    private Integer itemId;
    /**
     * 附件ID
     */
    @TableField(value = "attachment_id")
    private Integer attachmentId;

   /**
     * 关联的OSS ID
     */
    @TableField(value = "oss_id")
    private Long ossId;

    /**
     * 附件名称
     */
    @TableField(value = "attachment_name")
    private String attachmentName;


    /**
     * 附件类型
     */
    @TableField(value = "type_id")
    private Integer typeId;

    /**
     * 附件状态
     */
    @TableField(value = "state_id")
    private Integer stateId;

    /**
     * 上传创建时间
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * 上传创建人
     */
    @TableField(value = "created_by")
    private Integer createdBy;
}
